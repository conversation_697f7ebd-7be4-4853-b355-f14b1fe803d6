# BILI-TOOL Architecture Documentation

This document provides a comprehensive overview of the BILI-TOOL platform architecture, design decisions, and technical implementation details.

## System Overview

BILI-TOOL is a modern, full-stack web application built with Next.js 15, designed to provide a comprehensive AI tools directory with multilingual support, user authentication, and payment processing capabilities.

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Side   │    │   Server Side   │    │   External      │
│                 │    │                 │    │   Services      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • React 18      │    │ • Next.js 15    │    │ • PostgreSQL    │
│ • TypeScript    │◄──►│ • API Routes    │◄──►│ • Stripe        │
│ • Tailwind CSS  │    │ • Prisma ORM    │    │ • OAuth         │
│ • Radix UI      │    │ • NextAuth.js   │    │ • CDN           │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Technology Stack

### Frontend Technologies

#### Core Framework
- **Next.js 15.2.3** - React framework with App Router
- **React 18.2.0** - UI library with concurrent features
- **TypeScript 5.0** - Type-safe JavaScript development

#### Styling & UI
- **Tailwind CSS 3.4.1** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **Framer Motion 12.6.2** - Animation library
- **Lucide React** - Icon library

#### State Management
- **React Hooks** - Built-in state management
- **Server Components** - Server-side state handling
- **NextAuth.js** - Authentication state

### Backend Technologies

#### Runtime & Framework
- **Node.js 18+** - JavaScript runtime
- **Next.js API Routes** - Serverless API endpoints
- **Prisma 6.1.0** - Database ORM and query builder

#### Database
- **PostgreSQL 13+** - Primary database
- **Prisma Client** - Type-safe database access
- **Connection pooling** - Optimized database connections

#### Authentication & Security
- **NextAuth.js 4.24.11** - Authentication framework
- **OAuth 2.0** - Third-party authentication
- **JWT tokens** - Session management
- **bcryptjs** - Password hashing

#### Payment Processing
- **Stripe 17.7.0** - Payment processing
- **Webhooks** - Real-time payment updates
- **Subscription management** - Recurring payments

### Development & Deployment

#### Package Management
- **pnpm 10.12.1** - Fast, disk space efficient package manager
- **Node.js 18+** - Runtime environment

#### Testing
- **Jest 29.7.0** - Testing framework
- **ts-jest** - TypeScript support for Jest
- **Testing Library** - Component testing utilities

#### Build & Deployment
- **Turbopack** - Fast bundler for development
- **Docker** - Containerization
- **Vercel** - Deployment platform (recommended)

## Application Architecture

### Directory Structure

```
bili-tool/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # Internationalized routes
│   │   │   ├── auth/          # Authentication pages
│   │   │   ├── categories/    # Category pages
│   │   │   ├── tools/         # Tool pages
│   │   │   ├── orders/        # Order management
│   │   │   └── profile/       # User profile
│   │   ├── api/               # API routes
│   │   │   ├── auth/          # Authentication endpoints
│   │   │   ├── tools/         # Tools API
│   │   │   ├── categories/    # Categories API
│   │   │   ├── orders/        # Orders API
│   │   │   ├── users/         # Users API
│   │   │   └── stripe/        # Payment endpoints
│   │   ├── globals.css        # Global styles
│   │   └── layout.tsx         # Root layout
│   ├── components/            # Reusable components
│   │   ├── ui/               # Base UI components
│   │   ├── sections/         # Page sections
│   │   └── blocks/           # Content blocks
│   ├── lib/                  # Utility functions
│   │   ├── prisma.ts         # Database client
│   │   └── utils.ts          # Helper functions
│   ├── types/                # TypeScript definitions
│   ├── constants/            # Application constants
│   ├── i18n/                 # Internationalization
│   ├── auth.config.ts        # Authentication config
│   └── middleware.ts         # Next.js middleware
├── prisma/
│   └── schema.prisma         # Database schema
├── messages/                 # Translation files
├── public/                   # Static assets
├── docs/                     # Documentation
└── tests/                    # Test files
```

### Component Architecture

#### Component Hierarchy

```
App Layout
├── Header
│   ├── Navigation
│   ├── LanguageSwitcher
│   ├── ThemeToggle
│   └── AuthCheck
├── Main Content
│   ├── Hero Section
│   ├── SearchTools
│   ├── ToolsGrid
│   │   └── ToolCard[]
│   └── CategorySections
└── Footer
```

#### Component Design Principles

1. **Single Responsibility** - Each component has one clear purpose
2. **Composition over Inheritance** - Use composition for flexibility
3. **Props Interface** - Well-defined TypeScript interfaces
4. **Accessibility** - WCAG 2.1 compliance with Radix UI
5. **Performance** - Optimized rendering with React best practices

### Data Flow Architecture

#### Client-Server Communication

```
Client Component
    ↓ (API Request)
Next.js API Route
    ↓ (Database Query)
Prisma Client
    ↓ (SQL Query)
PostgreSQL Database
    ↑ (Result Set)
Prisma Client
    ↑ (Typed Objects)
API Route
    ↑ (JSON Response)
Client Component
```

#### State Management Flow

1. **Server State** - Managed by Next.js Server Components
2. **Client State** - Managed by React hooks (useState, useReducer)
3. **Authentication State** - Managed by NextAuth.js
4. **Form State** - Managed by controlled components
5. **Cache State** - Managed by Next.js caching mechanisms

## Database Architecture

### Schema Design

#### Core Entities

1. **Users** - User accounts and authentication
2. **Tools** - AI tools catalog
3. **Categories** - Hierarchical categorization
4. **Tags** - Flexible tagging system
5. **Orders** - Payment and subscription tracking

#### Multilingual Support

```sql
-- Base entity (language-agnostic)
tools (id, tool_id, icon_url, url, pricing_type, ...)

-- Translations (language-specific)
tool_translations (tool_id, locale, name, description, ...)
```

#### Relationships

```
Users (1) ──── (N) Orders
Tools (N) ──── (N) Categories (via tool_categories)
Tools (N) ──── (N) Tags (via tool_tags)
Tools (1) ──── (N) ToolTranslations
Categories (1) ──── (N) CategoryTranslations
Categories (1) ──── (N) Categories (self-referencing)
```

### Database Optimization

#### Indexing Strategy

```sql
-- Primary indexes
CREATE INDEX idx_tools_tool_id ON tools(tool_id);
CREATE INDEX idx_categories_slug ON categories(slug);

-- Composite indexes for queries
CREATE INDEX idx_tool_translations_tool_locale ON tool_translations(tool_id, locale);
CREATE INDEX idx_category_translations_category_locale ON category_translations(category_id, locale);

-- Performance indexes
CREATE INDEX idx_tools_is_featured ON tools(is_featured);
CREATE INDEX idx_tools_created_at ON tools(created_at);
```

#### Query Optimization

1. **Selective Loading** - Only fetch required fields
2. **Eager Loading** - Include related data in single query
3. **Pagination** - Limit result sets for performance
4. **Caching** - Cache frequently accessed data

## API Architecture

### RESTful Design

#### Endpoint Structure

```
/api/tools              # GET, POST
/api/tools/[id]         # GET, PUT, DELETE
/api/categories         # GET, POST
/api/categories/[slug]  # GET, PUT, DELETE
/api/users              # GET, POST
/api/orders             # GET, POST
/api/auth/*             # Authentication endpoints
/api/stripe/*           # Payment endpoints
```

#### Response Format

```typescript
// Success Response
{
  data: T,
  pagination?: {
    total: number,
    page: number,
    limit: number,
    totalPages: number
  }
}

// Error Response
{
  error: string,
  code?: string,
  details?: any
}
```

### Authentication Flow

```
1. User initiates login
2. Redirect to OAuth provider
3. Provider returns authorization code
4. Exchange code for access token
5. Fetch user profile
6. Create/update user in database
7. Generate JWT session token
8. Set secure HTTP-only cookie
9. Redirect to application
```

## Security Architecture

### Authentication Security

1. **OAuth 2.0** - Secure third-party authentication
2. **JWT Tokens** - Stateless session management
3. **Secure Cookies** - HTTP-only, secure, SameSite
4. **CSRF Protection** - Built-in Next.js protection

### Data Security

1. **Input Validation** - Server-side validation for all inputs
2. **SQL Injection Prevention** - Parameterized queries with Prisma
3. **XSS Protection** - Content Security Policy headers
4. **Environment Variables** - Secure secret management

### API Security

1. **Rate Limiting** - Prevent abuse and DoS attacks
2. **CORS Configuration** - Restrict cross-origin requests
3. **Authentication Middleware** - Protect sensitive endpoints
4. **Request Validation** - Validate request format and content

## Performance Architecture

### Caching Strategy

#### Client-Side Caching

1. **Browser Cache** - Static assets with long TTL
2. **Service Worker** - Offline functionality (future)
3. **React Query** - API response caching (if implemented)

#### Server-Side Caching

1. **Next.js Cache** - Page and API route caching
2. **Database Query Cache** - Prisma query result caching
3. **CDN Cache** - Static asset distribution
4. **Redis Cache** - Session and API caching (optional)

### Optimization Techniques

1. **Code Splitting** - Dynamic imports for large components
2. **Image Optimization** - Next.js Image component
3. **Bundle Analysis** - Regular bundle size monitoring
4. **Database Optimization** - Query optimization and indexing

## Internationalization Architecture

### i18n Implementation

```typescript
// Locale configuration
export const locales = ['en', 'zh'] as const;
export const defaultLocale = 'en';

// Message structure
{
  "tools": {
    "title": "AI Tools",
    "description": "Discover the best AI tools"
  }
}
```

### Content Localization

1. **Database Level** - Multilingual content in translation tables
2. **UI Level** - next-intl for interface translations
3. **URL Level** - Locale-based routing
4. **SEO Level** - Localized meta tags and sitemaps

## Deployment Architecture

### Production Environment

```
Load Balancer
    ↓
Next.js Application (Multiple Instances)
    ↓
Database Connection Pool
    ↓
PostgreSQL Database (Primary/Replica)
```

### Scalability Considerations

1. **Horizontal Scaling** - Multiple application instances
2. **Database Scaling** - Read replicas and connection pooling
3. **CDN Integration** - Global content distribution
4. **Caching Layers** - Multiple levels of caching

### Monitoring & Observability

1. **Application Monitoring** - Error tracking and performance metrics
2. **Database Monitoring** - Query performance and resource usage
3. **Infrastructure Monitoring** - Server health and resource utilization
4. **User Analytics** - Usage patterns and feature adoption

## Future Architecture Considerations

### Planned Enhancements

1. **Microservices** - Split into domain-specific services
2. **Event-Driven Architecture** - Implement event sourcing
3. **GraphQL API** - Alternative to REST for complex queries
4. **Real-time Features** - WebSocket integration for live updates
5. **AI/ML Integration** - Recommendation engine and content analysis

### Scalability Roadmap

1. **Phase 1** - Optimize current monolithic architecture
2. **Phase 2** - Implement caching and CDN
3. **Phase 3** - Database optimization and read replicas
4. **Phase 4** - Microservices migration
5. **Phase 5** - Event-driven architecture implementation
