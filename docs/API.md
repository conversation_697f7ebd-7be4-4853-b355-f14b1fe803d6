# BILI-TOOL API Documentation

This document provides comprehensive documentation for the BILI-TOOL API endpoints.

## Base URL

```
Development: http://localhost:3000/api
Production: https://bili-tool.com/api
```

## Authentication

BILI-TOOL uses NextAuth.js for authentication with multiple providers:

### Supported Authentication Methods

1. **Google OAuth** - `GET /api/auth/signin/google`
2. **GitHub OAuth** - `GET /api/auth/signin/github`
3. **Google One Tap** - `POST /api/auth/signin/google-one-tap`
4. **Email/Password** - `POST /api/auth/signin/credentials`

### Authentication Endpoints

#### User Registration
```http
POST /api/auth/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User created successfully",
  "userId": 123,
  "email": "<EMAIL>"
}
```

#### Get Current Session
```http
GET /api/auth/session
```

**Response:**
```json
{
  "user": {
    "id": "123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "image": "https://example.com/avatar.jpg",
    "uuid": "user-uuid"
  },
  "expires": "2024-01-01T00:00:00.000Z"
}
```

## Tools API

### Get Tools List
```http
GET /api/tools?page=1&limit=20&category=ai-writing&search=chatgpt&sort=newest
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `category` (optional): Filter by category slug
- `search` (optional): Search term for tool names and descriptions
- `sort` (optional): Sort order (`newest`, `oldest`, `rating`, `name`)
- `pricing` (optional): Filter by pricing type (`free`, `freemium`, `paid`)
- `featured` (optional): Filter featured tools (`true`/`false`)
- `new` (optional): Filter new tools (`true`/`false`)

**Response:**
```json
{
  "tools": [
    {
      "id": 1,
      "toolId": "chatgpt",
      "iconUrl": "https://example.com/icon.png",
      "url": "https://chat.openai.com",
      "pricingType": "freemium",
      "isPremium": false,
      "isNew": false,
      "isFeatured": true,
      "rating": 4.8,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "translations": [
        {
          "name": "ChatGPT",
          "description": "AI-powered conversational assistant"
        }
      ],
      "categories": [
        {
          "category": {
            "slug": "ai-writing",
            "translations": [
              {
                "name": "AI Writing"
              }
            ]
          }
        }
      ]
    }
  ],
  "pagination": {
    "total": 150,
    "page": 1,
    "limit": 20,
    "totalPages": 8
  }
}
```

### Get Tool Details
```http
GET /api/tools/[toolId]?locale=en
```

**Response:**
```json
{
  "id": 1,
  "toolId": "chatgpt",
  "iconUrl": "https://example.com/icon.png",
  "url": "https://chat.openai.com",
  "websiteLogo": "https://example.com/logo.png",
  "pricingType": "freemium",
  "isPremium": false,
  "isNew": false,
  "isFeatured": true,
  "rating": 4.8,
  "apiAvailable": true,
  "publisher": "OpenAI",
  "publisherUrl": "https://openai.com",
  "translations": [
    {
      "name": "ChatGPT",
      "description": "AI-powered conversational assistant",
      "longDescription": "Detailed description...",
      "usageInstructions": "How to use...",
      "pricingDetails": "Pricing information...",
      "integrationInfo": "API integration details..."
    }
  ],
  "categories": [...],
  "tags": [...],
  "features": [
    {
      "feature": "Natural language processing"
    }
  ]
}
```

## Categories API

### Get All Categories
```http
GET /api/categories?locale=en&level=1
```

**Query Parameters:**
- `locale` (optional): Language code (default: 'en')
- `level` (optional): Category level (1 for main categories, 2 for subcategories)

**Response:**
```json
{
  "categories": [
    {
      "id": 1,
      "slug": "ai-writing",
      "iconUrl": "https://example.com/icon.png",
      "level": 1,
      "weight": 100,
      "translations": [
        {
          "name": "AI Writing",
          "description": "AI-powered writing tools"
        }
      ],
      "children": [...]
    }
  ]
}
```

### Get Category Details
```http
GET /api/categories/[slug]?locale=en&page=1&limit=20
```

**Response:**
```json
{
  "category": {
    "id": 1,
    "slug": "ai-writing",
    "iconUrl": "https://example.com/icon.png",
    "level": 1,
    "translations": [
      {
        "name": "AI Writing",
        "description": "AI-powered writing tools"
      }
    ]
  },
  "tools": [...],
  "pagination": {
    "total": 45,
    "page": 1,
    "limit": 20,
    "totalPages": 3
  }
}
```

## Users API

### Get Users (Admin Only)
```http
GET /api/users
Authorization: Bearer <session-token>
```

**Response:**
```json
{
  "users": [
    {
      "id": 1,
      "uuid": "user-uuid",
      "email": "<EMAIL>",
      "nickname": "John Doe",
      "avatarUrl": "https://example.com/avatar.jpg",
      "locale": "en",
      "signinProvider": "google",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "orders": [...]
    }
  ]
}
```

### Create User
```http
POST /api/users
Content-Type: application/json

{
  "uuid": "user-uuid",
  "email": "<EMAIL>",
  "nickname": "John Doe",
  "avatarUrl": "https://example.com/avatar.jpg",
  "locale": "en",
  "signinType": "oauth",
  "signinProvider": "google",
  "signinOpenid": "google-user-id"
}
```

## Orders API

### Get User Orders
```http
GET /api/orders
Authorization: Bearer <session-token>
```

**Response:**
```json
{
  "orders": [
    {
      "id": 1,
      "orderNo": "ORDER-123456",
      "amount": 2999,
      "currency": "USD",
      "status": "paid",
      "credits": 1000,
      "productName": "Premium Plan",
      "validMonths": 12,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "paidAt": "2024-01-01T00:05:00.000Z"
    }
  ]
}
```

### Create Order
```http
POST /api/orders
Authorization: Bearer <session-token>
Content-Type: application/json

{
  "orderNo": "ORDER-123456",
  "userUuid": "user-uuid",
  "userEmail": "<EMAIL>",
  "amount": 2999,
  "currency": "USD",
  "status": "pending",
  "credits": 1000,
  "productId": "prod_123",
  "productName": "Premium Plan",
  "validMonths": 12
}
```

## Payment API (Stripe)

### Create Payment Session
```http
POST /api/stripe
Authorization: Bearer <session-token>
Content-Type: application/json

{
  "price": 2999,
  "email": "<EMAIL>",
  "productName": "Premium Plan",
  "successUrl": "https://bili-tool.com/success",
  "cancelUrl": "https://bili-tool.com/cancel"
}
```

**Response:**
```json
{
  "sessionId": "cs_test_123456",
  "url": "https://checkout.stripe.com/pay/cs_test_123456"
}
```

### Stripe Webhook
```http
POST /api/stripe/webhook
Stripe-Signature: <stripe-signature>
Content-Type: application/json

{
  "type": "checkout.session.completed",
  "data": {
    "object": {
      "id": "cs_test_123456",
      "payment_status": "paid",
      "customer_email": "<EMAIL>",
      "amount_total": 2999
    }
  }
}
```

## Error Responses

All API endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": "Additional error details"
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `500` - Internal Server Error

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Authentication endpoints**: 5 requests per minute per IP
- **Tools API**: 100 requests per minute per IP
- **Categories API**: 50 requests per minute per IP
- **User-specific endpoints**: 30 requests per minute per user

## Caching

API responses are cached for optimal performance:

- **Tools list**: 10 minutes
- **Category data**: 10 minutes
- **Tool details**: 1 hour
- **Static content**: 24 hours

Cache headers are included in responses:
```
Cache-Control: public, s-maxage=600, stale-while-revalidate=86400
```
