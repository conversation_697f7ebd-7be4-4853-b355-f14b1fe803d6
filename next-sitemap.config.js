/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://www.bili-tool.com',
  generateRobotsTxt: false, // We're using app/robots.ts instead
  generateIndexSitemap: true,
  sitemapSize: 7000,
  changefreq: 'daily',
  priority: 0.7,

  // 支持国际化的站点地图
  alternateRefs: [
    {
      href: (process.env.SITE_URL || 'https://www.bili-tool.com') + '/en',
      hreflang: 'en',
    },
    {
      href: (process.env.SITE_URL || 'https://www.bili-tool.com') + '/zh',
      hreflang: 'zh',
    },
    {
      href: (process.env.SITE_URL || 'https://www.bili-tool.com'),
      hreflang: 'x-default',
    },
  ],

  // 排除一些路径
  exclude: [
    '/404',
    '/500',
    '/auth/*',
    '/api/*',
    '/_next/*',
    '/admin/*',
    '/private/*',
    '/profile/*',
    '/orders/*',
    '/sitemap.xml',
    '/robots.txt',
  ],
  // 自定义转换函数
  transform: async (config, path) => {
    // 为不同类型的页面设置不同的优先级和更新频率
    let priority = 0.7;
    let changefreq = 'weekly';

    if (path === '/en' || path === '/zh' || path === '/') {
      priority = 1.0;
      changefreq = 'daily';
    } else if (path.includes('/tools/')) {
      priority = 0.8;
      changefreq = 'weekly';
    } else if (path.includes('/categories/')) {
      priority = 0.6;
      changefreq = 'weekly';
    } else if (path.includes('/tools')) {
      priority = 0.9;
      changefreq = 'daily';
    }

    return {
      loc: path,
      changefreq,
      priority,
      lastmod: new Date().toISOString(),
      alternateRefs: config.alternateRefs ?? [],
    };
  },

  // 手动添加额外路径
  additionalPaths: async (config) => {
    const result = [];

    // 添加重要的静态页面
    const staticPages = [
      { loc: '/en', priority: 1.0, changefreq: 'daily' },
      { loc: '/zh', priority: 1.0, changefreq: 'daily' },
      { loc: '/en/tools', priority: 0.9, changefreq: 'daily' },
      { loc: '/zh/tools', priority: 0.9, changefreq: 'daily' },
      { loc: '/en/categories', priority: 0.8, changefreq: 'weekly' },
      { loc: '/zh/categories', priority: 0.8, changefreq: 'weekly' },
      { loc: '/en/privacy', priority: 0.6, changefreq: 'monthly' },
      { loc: '/zh/privacy', priority: 0.6, changefreq: 'monthly' },
      { loc: '/en/terms', priority: 0.6, changefreq: 'monthly' },
      { loc: '/zh/terms', priority: 0.6, changefreq: 'monthly' },
    ];

    for (const page of staticPages) {
      result.push({
        ...page,
        lastmod: new Date().toISOString(),
        alternateRefs: config.alternateRefs ?? [],
      });
    }

    return result;
  },

  // 机器人文件选项（虽然我们使用app/robots.ts，但保留作为备份）
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/auth/',
          '/admin/',
          '/private/',
          '/_next/',
          '/profile/',
          '/orders/',
        ],
      },
      {
        userAgent: 'GPTBot',
        disallow: '/',
      },
      {
        userAgent: 'ChatGPT-User',
        disallow: '/',
      },
    ],
    additionalSitemaps: [
      (process.env.SITE_URL || 'https://www.bili-tool.com') + '/sitemap.xml',
    ],
  },
};