"use client";

import Link from "next/link";
import { Button } from "./ui/button";
import { usePathname } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Menu, Globe, Search, Sparkles, User } from "lucide-react";
import { signIn, signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HeaderProps {
  header: {
    logo: string;
    nav: {
      discover: string;
      categories: string;
      submitAI: string;
      pricing: string;
      search: string;
      features: string;
      examples: string;
      docs: string;
    };
    cta: {
      login: string;
      signup: string;
    };
    userMenu: {
      myOrders: string;
      signOut: string;
      profile: string;
    };
  };
}

export default function Header({ header }: HeaderProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const switchLocale = (locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    window.location.href = newPathname;
  };

  return (
    <header className="fixed w-full top-0 z-50">
      {/* Top promotional banner */}
      {/* <div className={`relative overflow-hidden transition-all duration-300 ease-in-out ${
        isTopBannerVisible ? 'h-auto opacity-100 translate-y-0' : 'h-0 opacity-0 -translate-y-full overflow-hidden'
      }`}>
        <div className="absolute inset-0 bg-background/80 backdrop-blur-md supports-[backdrop-filter]:bg-background/60"></div>
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"></div>
        <div className="relative flex items-center justify-center px-4 py-2">
          <Link
            href="https://openart.ai/home?ref=aiwithme"
            target="_blank"
            rel="nofollow noopener noreferrer sponsored"
            className="group inline-flex items-center text-sm font-medium text-primary hover:text-primary/90 transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] relative z-10"
          >
            <span className="mr-2 group-hover:opacity-100 transition-opacity duration-200">✨</span>
            <span className="bg-primary/80 group-hover:bg-primary rounded-md p-1 text-white mr-2">OpenArt</span>
            <span className="relative">
              <span>Free AI video & image generator • Limited-time 50% off</span>
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary/40 group-hover:w-full transition-all duration-300 ease-out"></span>
            </span>
            <span className="ml-2 group-hover:opacity-100 group-hover:translate-x-0.5 transition-all duration-200 w-5 h-5 bg-primary/80 group-hover:bg-primary rounded-full text-white flex items-center justify-center text-xs">
              →
            </span>
          </Link>
          <button
            onClick={handleCloseBanner}
            className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-foreground/10 transition-colors duration-200 z-20 group"
            title="Close top ad"
            aria-label="close top ad"
          >
            <X className="text-foreground/60 group-hover:text-foreground/80 transition-colors duration-200 h-4 w-4" />
          </button>
        </div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
      </div> */}

      {/* Main navigation */}
      <nav className="mx-auto h-auto w-full bg-background/80 backdrop-blur-md supports-[backdrop-filter]:bg-background/60 text-foreground px-5 lg:px-4 xl:px-1 border-b border-border/50 shadow-sm">
        <div className="lg:container mx-auto flex flex-col lg:flex-row lg:items-center justify-between py-3 lg:py-0 relative">
          {/* Logo and mobile menu toggle */}
          <div className="flex items-center justify-between lg:justify-start">
            <Link
              href="/"
              className="cursor-pointer flex items-center leading-loose transition-all duration-200 hover:scale-105 group"
              title={header.logo}
            >
              <Image
                src="https://img.aistak.com/logo.png"
                width={40}
                height={40}
                alt={`${header.logo} logo`}
                className="inline-block h-10 w-10 object-contain transition-transform duration-200 group-hover:rotate-12"
              />
              <span className="ml-2">
                <span className="text-primary font-semibold">Bili</span>
                <span className="font-medium"> -Tool.COM</span>
              </span>
            </Link>

            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              title="Toggle Menu"
              aria-label="Toggle Menu"
              className="lg:hidden p-2 rounded-lg hover:bg-secondary/50 transition-colors duration-200"
            >
              <Menu className="h-6 w-6" />
            </button>
          </div>

          {/* Desktop navigation */}
          <div className="hidden lg:flex lg:items-center lg:space-x-1">
            <Link
              href="/tools/"
              className="group flex items-center px-4 py-3 text-sm font-medium text-foreground/80 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
            >
              <span className="text-primary mr-2 group-hover:scale-110 transition-transform duration-200">
                <Sparkles className="h-4 w-4" />
              </span>
              {header.nav.discover}
            </Link>
            <Link
              href="/categories/"
              className="group flex items-center px-4 py-3 text-sm font-medium text-foreground/80 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
            >
              <span className="text-primary mr-2 group-hover:scale-110 transition-transform duration-200">
                <Sparkles className="h-4 w-4" />
              </span>
              {header.nav.categories}
            </Link>
            {/* <Link
              href="/submit/"
              className="group flex items-center px-4 py-3 text-sm font-medium text-foreground/80 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
            >
              <span className="text-primary mr-2 group-hover:scale-110 transition-transform duration-200">
                <Sparkles className="h-4 w-4" />
              </span>
              Submit AI
            </Link> */}
            {/* <Link
              href="/pricing/"
              className="group flex items-center px-4 py-3 text-sm font-medium text-foreground/80 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
            >
              <span className="text-primary mr-2 group-hover:scale-110 transition-transform duration-200">
                <Sparkles className="h-4 w-4" />
              </span>
              Pricing
            </Link> */}
          </div>

          {/* Desktop right section */}
          <div className="hidden lg:flex lg:items-center lg:space-x-3">
            {/* Search button */}
            {/* <button className="inline-flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground transition-colors" title="Search (⌘K)">
              <Search className="w-4 h-4" />
              <span className="hidden sm:inline">Search</span>
              <kbd className="pointer-events-none hidden sm:inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span>⌘</span>
                <span>K</span>
              </kbd>
            </button> */}

            {/* Language toggle */}
            <div className="flex items-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 py-2 flex items-center gap-2 px-3">
                    <Globe className="h-[1.2rem] w-[1.2rem]" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  {locales.map((locale) => (
                    <DropdownMenuItem
                      key={locale}
                      onClick={() => switchLocale(locale)}
                      className="cursor-pointer"
                    >
                      {localeNames[locale]}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* User menu */}
            {session ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="focus:outline-none">
                    <span className="relative flex shrink-0 overflow-hidden h-8 w-8 rounded-full ring-2 ring-transparent hover:ring-primary/20 transition-all duration-200">
                      {session.user?.image ? (
                        <Image
                          className="h-full w-full rounded-full object-cover"
                          src={session.user.image}
                          alt={session.user.name || ''}
                          width={32}
                          height={32}
                          unoptimized
                          priority
                        />
                      ) : (
                        <span className="flex h-full w-full items-center justify-center dark:bg-zinc-800 rounded-full bg-primary/10 text-primary">
                          <User className="h-4 w-4" />
                        </span>
                      )}
                    </span>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <div className="px-4 py-2 text-sm text-gray-700">
                    <div className="font-medium">{session.user?.name}</div>
                    <div className="text-gray-500">{session.user?.email}</div>
                  </div>
                  <DropdownMenuItem asChild>
                    <Link href={`/${currentLocale}/profile`}>
                      {header.userMenu.profile}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}>
                    {header.userMenu.signOut}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                onClick={() => signIn()}
                size="sm"
                className="bg-primary hover:bg-primary/90 text-white rounded-full px-6"
              >
                {header.cta.login}
              </Button>
            )}
          </div>

          {/* Mobile menu */}
          <div className={`lg:hidden absolute top-full left-0 right-0 bg-background/95 backdrop-blur-md border-b border-border/50 shadow-lg transition-all duration-300 ease-in-out ${
            isMobileMenuOpen ? 'opacity-100 translate-y-0 visible' : 'opacity-0 -translate-y-4 invisible'
          }`}>
            <div className="p-4 space-y-4">
              <Link
                href="/tools/"
                className="group flex items-center p-3 text-foreground/80 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
              >
                <span className="text-primary mr-3 group-hover:scale-110 transition-transform duration-200">
                  <Sparkles className="h-4 w-4" />
                </span>
                <span className="font-medium">{header.nav.discover}</span>
              </Link>
              <Link
                href="/categories/"
                className="group flex items-center p-3 text-foreground/80 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
              >
                <span className="text-primary mr-3 group-hover:scale-110 transition-transform duration-200">
                  <Sparkles className="h-4 w-4" />
                </span>
                <span className="font-medium">{header.nav.categories}</span>
              </Link>
              <Link
                href="/submit/"
                className="group flex items-center p-3 text-foreground/80 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
              >
                <span className="text-primary mr-3 group-hover:scale-110 transition-transform duration-200">
                  <Sparkles className="h-4 w-4" />
                </span>
                <span className="font-medium">{header.nav.submitAI}</span>
              </Link>
              <Link
                href="/pricing/"
                className="group flex items-center p-3 text-foreground/80 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
              >
                <span className="text-primary mr-3 group-hover:scale-110 transition-transform duration-200">
                  <Sparkles className="h-4 w-4" />
                </span>
                <span className="font-medium">{header.nav.pricing}</span>
              </Link>

              <div className="border-t border-border/30 pt-4 flex items-center justify-between">
                <button className="inline-flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground transition-colors" title="Search (⌘K)">
                  <Search className="w-4 h-4" />
                  <span>{header.nav.search}</span>
                  <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                    <span>⌘</span>
                    <span>K</span>
                  </kbd>
                </button>

                <div className="flex items-center space-x-3">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="h-9 py-2 flex items-center gap-2 px-3">
                        <Globe className="h-[1.2rem] w-[1.2rem]" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      {locales.map((locale) => (
                        <DropdownMenuItem
                          key={locale}
                          onClick={() => switchLocale(locale)}
                          className="cursor-pointer"
                        >
                          {localeNames[locale]}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {session ? (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <button className="focus:outline-none">
                          <span className="relative flex shrink-0 overflow-hidden h-8 w-8 rounded-full ring-2 ring-transparent hover:ring-primary/20 transition-all duration-200">
                            {session.user?.image ? (
                              <Image
                                className="h-full w-full rounded-full object-cover"
                                src={session.user.image}
                                alt={session.user.name || ''}
                                width={32}
                                height={32}
                                unoptimized
                                priority
                              />
                            ) : (
                              <span className="flex h-full w-full items-center justify-center dark:bg-zinc-800 rounded-full bg-primary/10 text-primary">
                                <User className="h-4 w-4" />
                              </span>
                            )}
                          </span>
                        </button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <div className="px-4 py-2 text-sm text-gray-700">
                          <div className="font-medium">{session.user?.name}</div>
                          <div className="text-gray-500">{session.user?.email}</div>
                        </div>
                        <DropdownMenuItem asChild>
                          <Link href={`/${currentLocale}/profile`}>
                            {header.userMenu.profile}
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}>
                          {header.userMenu.signOut}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ) : (
                    <Button
                      onClick={() => signIn()}
                      size="sm"
                      className="bg-primary hover:bg-primary/90 text-white rounded-full"
                    >
                      {header.cta.login}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
}