"use client"

import * as React from "react"
import { Link } from "@/i18n/routing"
import Image from "next/image"
import { X, Mail } from "lucide-react"

interface FooterProps {
  footer: {
    description: string;
    toolCategories: {
      title: string;
      image: string;
      text: string;
      "3d": string;
      video: string;
      codeIt: string;
      productivity: string;
      textWriting: string;
      chatbot: string;
      voice: string;
      business: string;
      marketing: string;
      lifeAssistant: string;
      education: string;
      prompt: string;
      aiDetector: string;
      other: string;
    };
    article: {
      title: string;
    };
    followUs: {
      title: string;
    };
    contactUs: {
      title: string;
    };
    copyright: string;
    and: string;
    legal: {
      privacy: string;
      terms: string;
    };
  };
}

function Footer({ footer }: FooterProps) {
  return (
    <footer className="bg-background border-t border-border/50">
      <div className="lg:container mx-auto px-5 lg:px-4 xl:px-1 py-12">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-5">
          {/* Website Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-4">
              <Image
                src="https://img.aistak.com/logo.png"
                width={32}
                height={32}
                alt="BILI-TOOL.COM logo"
                className="inline-block h-8 w-8 object-contain"
              />
              <span className="ml-2 font-semibold">
                {/* <span className="text-primary">AI</span>
                <span>STAK</span> */}
                <span>Bili-Tool.Com</span>
              </span>
            </div>
            <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
              {footer.description}
            </p>
          </div>

          {/* Tool Categories */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">{footer.toolCategories.title}</h3>
            <nav className="space-y-2 text-sm">
              <Link href="/tools?category=image" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.image}
              </Link>
              <Link href="/tools?category=text" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.text}
              </Link>
              <Link href="/tools?category=3d" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories["3d"]}
              </Link>
              <Link href="/tools?category=video" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.video}
              </Link>
              <Link href="/tools?category=code" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.codeIt}
              </Link>
              <Link href="/tools?category=productivity" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.productivity}
              </Link>
              <Link href="/tools?category=text-writing" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.textWriting}
              </Link>
              <Link href="/tools?category=chatbot" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.chatbot}
              </Link>
              <Link href="/tools?category=voice" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.voice}
              </Link>
              <Link href="/tools?category=business" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.business}
              </Link>
              <Link href="/tools?category=marketing" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.marketing}
              </Link>
              <Link href="/tools?category=life-assistant" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.lifeAssistant}
              </Link>
              <Link href="/tools?category=education" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.education}
              </Link>
              <Link href="/tools?category=prompt" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.prompt}
              </Link>
              <Link href="/tools?category=ai-detector" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.aiDetector}
              </Link>
              <Link href="/tools?category=other" className="block text-muted-foreground hover:text-primary transition-colors">
                {footer.toolCategories.other}
              </Link>
            </nav>
          </div>

          {/* Article */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">{footer.article.title}</h3>
            <nav className="space-y-2 text-sm">
              <a href="https://www.forbes.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Forbes
              </a>
              <a href="https://techcrunch.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                TechCrunch
              </a>
              <a href="https://www.computerworld.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Computerworld
              </a>
              <a href="https://venturebeat.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                VentureBeat
              </a>
              <a href="https://futurism.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Futurism
              </a>
              <a href="https://www.anthropic.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Anthropic
              </a>
              <a href="https://www.deeplearning.ai" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Deeplearning
              </a>
              <a href="https://deepmind.google" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Deepmind
              </a>
              <a href="https://huggingface.co" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Huggingface
              </a>
              <a href="https://stability.ai" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Stability
              </a>
              <a href="https://www.indiehackers.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Indiehackers
              </a>
              <a href="https://openai.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-primary transition-colors">
                Openai
              </a>
            </nav>
          </div>

          {/* Follow Us */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">{footer.followUs.title}</h3>
            <nav className="space-y-2 text-sm">
              <a href="https://x.com/aistak" target="_blank" rel="noopener noreferrer" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
                <X className="h-4 w-4 mr-2" />
                X
              </a>
            </nav>
          </div>

          {/* Contact Us */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">{footer.contactUs.title}</h3>
            <nav className="space-y-2 text-sm">
              <a href="mailto:<EMAIL>" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
                <Mail className="h-4 w-4 mr-2" />
                <EMAIL>
              </a>
            </nav>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-12 pt-8 border-t border-border/50 flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{footer.copyright}</span>
            <Link href="/privacy" className="hover:text-primary transition-colors">
              {footer.legal.privacy}
            </Link>
            <span>{footer.and}</span>
            <Link href="/terms" className="hover:text-primary transition-colors">
              {footer.legal.terms}
            </Link>
          </div>

          {/* <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>English</span>
            <span>中文</span>
            <span>日本語</span>
            <span>한국어</span>
          </div> */}
        </div>
      </div>
    </footer>
  )
}

export { Footer }
export type { FooterProps }