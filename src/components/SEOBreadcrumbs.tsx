'use client';

import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';
import Script from 'next/script';
import { generateBreadcrumbSchema } from '@/lib/seo';
import { useTranslations } from 'next-intl';

export interface BreadcrumbItem {
  name: string;
  href: string;
  current?: boolean;
}

interface SEOBreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
  homeLabel?: string;
  separator?: React.ReactNode;
  locale?: string;
}

export function SEOBreadcrumbs({
  items,
  className,
  showHome = true,
  homeLabel = 'Home',
  separator,
  locale = 'en',
}: SEOBreadcrumbsProps) {
  // Prepare breadcrumb items with home if needed
  const breadcrumbItems = showHome 
    ? [{ name: homeLabel, href: `/${locale}` }, ...items]
    : items;

  // Generate structured data
  const structuredData = generateBreadcrumbSchema(
    breadcrumbItems.map(item => ({
      name: item.name,
      url: item.href.startsWith('http') ? item.href : `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.bili-tool.com'}${item.href}`
    }))
  );

  const defaultSeparator = <ChevronRight size={16} className="text-muted-foreground" />;

  return (
    <>
      {/* Structured Data for Breadcrumbs */}
      <Script
        id="breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: structuredData,
        }}
      />
      
      {/* Visual Breadcrumbs */}
      <nav
        aria-label="Breadcrumb"
        className={cn('flex items-center space-x-1 text-sm', className)}
        itemScope
        itemType="https://schema.org/BreadcrumbList"
      >
        <ol className="flex items-center space-x-1">
          {breadcrumbItems.map((item, index) => (
            <li
              key={item.href}
              className="flex items-center"
              itemProp="itemListElement"
              itemScope
              itemType="https://schema.org/ListItem"
            >
              {index > 0 && (
                <span className="mx-2" aria-hidden="true">
                  {separator || defaultSeparator}
                </span>
              )}
              
              {item.current ? (
                <span
                  className="font-medium text-foreground"
                  aria-current="page"
                  itemProp="name"
                >
                  {showHome && index === 0 && <Home size={16} className="mr-1" />}
                  {item.name}
                </span>
              ) : (
                <Link
                  href={item.href}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                  itemProp="item"
                >
                  <span itemProp="name" className="flex items-center">
                    {showHome && index === 0 && <Home size={16} className="mr-1" />}
                    {item.name}
                  </span>
                </Link>
              )}
              
              <meta itemProp="position" content={String(index + 1)} />
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}

// Specialized breadcrumb for tool pages
export function ToolBreadcrumbs({
  tool,
  locale,
  className,
}: {
  tool: any;
  locale: string;
  className?: string;
}) {
  const t = useTranslations('breadcrumbs');
  const translation = tool.translations?.find((t: any) => t.locale === locale) || tool.translations?.[0];
  const toolName = translation?.name || tool.name || t('aiTool');

  // Get primary category for breadcrumb
  const primaryCategory = tool.categories?.[0]?.category;
  const categoryTranslation = primaryCategory?.translations?.find((t: any) => t.locale === locale) || primaryCategory?.translations?.[0];

  const items: BreadcrumbItem[] = [
    { name: t('tools'), href: `/${locale}/tools` },
  ];
  
  // Add category if available
  if (primaryCategory && categoryTranslation) {
    items.push({
      name: categoryTranslation.name,
      href: `/${locale}/categories/${primaryCategory.slug}`,
    });
  }
  
  // Add current tool
  items.push({
    name: toolName,
    href: `/${locale}/tools/${tool.toolId}`,
    current: true,
  });

  return (
    <SEOBreadcrumbs
      items={items}
      locale={locale}
      className={className}
      homeLabel={locale === 'zh' ? '首页' : 'Home'}
    />
  );
}

// Specialized breadcrumb for category pages
export function CategoryBreadcrumbs({
  category,
  locale,
  className,
}: {
  category: any;
  locale: string;
  className?: string;
}) {
  const translation = category.translations?.find((t: any) => t.locale === locale) || category.translations?.[0];
  const categoryName = translation?.name || category.name || 'Category';
  
  const items: BreadcrumbItem[] = [
    { name: 'Categories', href: `/${locale}/categories` },
  ];
  
  // Add parent category if this is a subcategory
  if (category.parent) {
    const parentTranslation = category.parent.translations?.find((t: any) => t.locale === locale) || category.parent.translations?.[0];
    if (parentTranslation) {
      items.push({
        name: parentTranslation.name,
        href: `/${locale}/categories/${category.parent.slug}`,
      });
    }
  }
  
  // Add current category
  items.push({
    name: categoryName,
    href: `/${locale}/categories/${category.slug}`,
    current: true,
  });

  return (
    <SEOBreadcrumbs
      items={items}
      locale={locale}
      className={className}
      homeLabel={locale === 'zh' ? '首页' : 'Home'}
    />
  );
}

// Simple breadcrumb for static pages
export function StaticPageBreadcrumbs({
  pageName,
  pageHref,
  locale,
  className,
  parentPages = [],
}: {
  pageName: string;
  pageHref: string;
  locale: string;
  className?: string;
  parentPages?: BreadcrumbItem[];
}) {
  const items: BreadcrumbItem[] = [
    ...parentPages,
    {
      name: pageName,
      href: pageHref,
      current: true,
    },
  ];

  return (
    <SEOBreadcrumbs
      items={items}
      locale={locale}
      className={className}
      homeLabel={locale === 'zh' ? '首页' : 'Home'}
    />
  );
}
