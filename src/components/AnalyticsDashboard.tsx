"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { <PERSON>, Eye, Clock, Mouse, Globe, <PERSON><PERSON>hart, <PERSON><PERSON>hart, TrendingUp, TrendingDown, Search } from 'lucide-react';

// 数据库查询接口类型
interface TrafficOverviewData {
  tool_id: string;
  domain: string;
  global_rank: number;
  country_rank: number;
  total_visits: string;
  total_visits_raw: string;
  visits_change_percent: string;
  avg_duration_seconds: number;
  pages_per_visit: string;
  bounce_rate: string;
  stat_date: string;
}

// 流量来源数据接口类型
interface TrafficSourceData {
  source_type: string;
  traffic_percent: number;
  traffic_percent_raw: string;
  estimated_visits: string;
  stat_date: string;
}

// 月度流量趋势数据接口类型
interface MonthlyTrendData {
  period: string;
  year_month: string;
  visits: string;
  visits_raw: string;
  growth_rate: string;
}

// 地理分布数据接口类型
interface RegionDistributionData {
  region_name: string;
  country_code: string | null;
  traffic_percent: number;
  traffic_percent_raw: string;
  estimated_visits: string;
  rank_in_region: number | null;
  stat_date: string;
}

// 关键词分析数据接口类型
interface KeywordAnalysisData {
  keyword: string;
  keyword_type: string | null;
  traffic_percent: string;
  traffic_percent_raw: string | null;
  estimated_visits: string;
  search_volume: string;
  keyword_difficulty: number | null;
  average_position: string;
  stat_date: string;
}

// 格式化数字显示
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// 格式化时长显示
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 获取流量数据
const fetchTrafficData = async (toolId: string): Promise<TrafficOverviewData | null> => {
  try {
    const response = await fetch(`/api/traffic-overview?tool_id=${toolId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch traffic data');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching traffic data:', error);
    return null;
  }
};

// 获取流量来源数据
const fetchTrafficSources = async (toolId: string): Promise<TrafficSourceData[]> => {
  try {
    const response = await fetch(`/api/traffic-sources?tool_id=${toolId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch traffic sources data');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching traffic sources data:', error);
    return [];
  }
};

// 获取月度流量趋势数据
const fetchMonthlyTrends = async (toolId: string): Promise<MonthlyTrendData[]> => {
  try {
    const response = await fetch(`/api/traffic-monthly-trends?tool_id=${toolId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch monthly trends data');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching monthly trends data:', error);
    return [];
  }
};

// 获取地理分布数据
const fetchRegionDistribution = async (toolId: string): Promise<RegionDistributionData[]> => {
  try {
    const response = await fetch(`/api/traffic-regions?tool_id=${toolId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch region distribution data');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching region distribution data:', error);
    return [];
  }
};

// 获取关键词分析数据
const fetchKeywordAnalysis = async (toolId: string): Promise<KeywordAnalysisData[]> => {
  try {
    const response = await fetch(`/api/traffic-keywords?tool_id=${toolId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch keyword analysis data');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching keyword analysis data:', error);
    return [];
  }
};

// 根据数据库数据生成分析指标
const generateAnalyticsMetrics = (data: TrafficOverviewData | null) => {
  if (!data) {
    // 默认数据，当没有数据时显示
    return [
      { label: "访问量", value: 0, displayValue: "0", icon: Users, color: "blue", trend: "0%", trendUp: true },
      { label: "跳出率", value: 0, displayValue: "0%", icon: Mouse, color: "green", trend: "0%", trendUp: false },
      { label: "页面访问数", value: 0, displayValue: "0", icon: Eye, color: "purple", trend: "0%", trendUp: true },
      { label: "访问时长", value: 0, displayValue: "00:00", icon: Clock, color: "orange", trend: "0%", trendUp: true },
      { label: "全球排名", value: 0, displayValue: "0", icon: Globe, color: "pink", trend: "0", trendUp: true },
      { label: "国家排名", value: 0, displayValue: "0", icon: Globe, color: "indigo", trend: "0", trendUp: true },
    ];
  }

  const visitChangePercent = parseFloat(data.visits_change_percent);
  const isVisitTrendUp = visitChangePercent >= 0;
  const visitTrendDisplay = isVisitTrendUp ? `+${visitChangePercent}%` : `${visitChangePercent}%`;

  return [
    {
      label: "访问量",
      value: parseInt(data.total_visits),
      displayValue: data.total_visits_raw || data.total_visits,
      icon: Users,
      color: "blue",
      trend: visitTrendDisplay,
      trendUp: isVisitTrendUp
    },
    {
      label: "跳出率",
      value: parseFloat(data.bounce_rate),
      displayValue: `${parseFloat(data.bounce_rate).toFixed(2)}%`,
      icon: Mouse,
      color: "green",
      trend: "0%", // 跳出率变化数据暂时没有
      trendUp: false
    },
    {
      label: "页面访问数",
      value: parseFloat(data.pages_per_visit),
      displayValue: parseFloat(data.pages_per_visit).toFixed(2),
      icon: Eye,
      color: "purple",
      trend: "0%", // 页面访问数变化数据暂时没有
      trendUp: true
    },
    {
      label: "访问时长",
      value: data.avg_duration_seconds,
      displayValue: formatDuration(data.avg_duration_seconds),
      icon: Clock,
      color: "orange",
      trend: "0%", // 访问时长变化数据暂时没有
      trendUp: true
    },
    {
      label: "全球排名",
      value: data.global_rank,
      displayValue: data.global_rank > 0 ? data.global_rank.toLocaleString() : "未排名",
      icon: Globe,
      color: "pink",
      trend: "0", // 排名变化数据暂时没有
      trendUp: true
    },
    {
      label: "国家排名",
      value: data.country_rank,
      displayValue: data.country_rank > 0 ? data.country_rank.toLocaleString() : "未排名",
      icon: Globe,
      color: "indigo",
      trend: "0", // 排名变化数据暂时没有
      trendUp: true
    },
  ];
};

// 流量来源类型映射
const sourceTypeMapping: Record<string, { label: string; color: string }> = {
  search: { label: "搜索引擎", color: "#10b981" },
  direct: { label: "直接访问", color: "#3b82f6" },
  social: { label: "社交媒体", color: "#f59e0b" },
  referrals: { label: "推荐链接", color: "#ef4444" },
  paidReferrals: { label: "付费推荐", color: "#8b5cf6" },
  mail: { label: "邮件营销", color: "#06b6d4" },
  display: { label: "展示广告", color: "#f97316" },
  other: { label: "其他", color: "#6b7280" },
};

// 根据数据库数据生成流量来源数据
const generateTrafficSources = (sources: TrafficSourceData[]) => {
  return sources.map(source => {
    const mapping = sourceTypeMapping[source.source_type] || sourceTypeMapping.other;
    return {
      source: mapping.label,
      value: source.traffic_percent,
      color: mapping.color,
      rawValue: source.traffic_percent_raw,
      estimatedVisits: source.estimated_visits,
    };
  }).sort((a, b) => b.value - a.value); // 按百分比降序排列
};

// 国家/地区映射配置
const regionMapping: Record<string, { label: string; flag: string; color: string }> = {
  // 主要国家
  'United States': { label: '美国', flag: '🇺🇸', color: '#3b82f6' },
  'United Kingdom': { label: '英国', flag: '🇬🇧', color: '#10b981' },
  'China': { label: '中国', flag: '🇨🇳', color: '#f59e0b' },
  'Japan': { label: '日本', flag: '🇯🇵', color: '#ef4444' },
  'Germany': { label: '德国', flag: '🇩🇪', color: '#8b5cf6' },
  'France': { label: '法国', flag: '🇫🇷', color: '#06b6d4' },
  'Canada': { label: '加拿大', flag: '🇨🇦', color: '#f97316' },
  'Australia': { label: '澳大利亚', flag: '🇦🇺', color: '#84cc16' },
  'Brazil': { label: '巴西', flag: '🇧🇷', color: '#22c55e' },
  'India': { label: '印度', flag: '🇮🇳', color: '#a855f7' },
  'South Korea': { label: '韩国', flag: '🇰🇷', color: '#ec4899' },
  'Italy': { label: '意大利', flag: '🇮🇹', color: '#14b8a6' },
  'Spain': { label: '西班牙', flag: '🇪🇸', color: '#f59e0b' },
  'Netherlands': { label: '荷兰', flag: '🇳🇱', color: '#3b82f6' },
  'Russia': { label: '俄罗斯', flag: '🇷🇺', color: '#ef4444' },
  'Mexico': { label: '墨西哥', flag: '🇲🇽', color: '#10b981' },
  'Turkey': { label: '土耳其', flag: '🇹🇷', color: '#f97316' },
  'Indonesia': { label: '印度尼西亚', flag: '🇮🇩', color: '#8b5cf6' },
  'Thailand': { label: '泰国', flag: '🇹🇭', color: '#06b6d4' },
  'Vietnam': { label: '越南', flag: '🇻🇳', color: '#84cc16' },
  'Philippines': { label: '菲律宾', flag: '🇵🇭', color: '#22c55e' },
  'Malaysia': { label: '马来西亚', flag: '🇲🇾', color: '#a855f7' },
  'Singapore': { label: '新加坡', flag: '🇸🇬', color: '#ec4899' },
  'Taiwan': { label: '台湾', flag: '🇹🇼', color: '#14b8a6' },
  'Hong Kong': { label: '香港', flag: '🇭🇰', color: '#f59e0b' },
  // 默认其他
  'Other': { label: '其他', flag: '🌍', color: '#6b7280' },
};

// 根据数据库数据生成地理分布数据
const generateGeographicData = (regions: RegionDistributionData[]) => {
  if (!regions || regions.length === 0) {
    // 默认数据，当没有数据时显示
    return [
      { country: "美国", flag: "🇺🇸", value: 0, color: "#3b82f6", rawValue: "0%", estimatedVisits: "0", regionName: "United States" },
      { country: "英国", flag: "🇬🇧", value: 0, color: "#10b981", rawValue: "0%", estimatedVisits: "0", regionName: "United Kingdom" },
      { country: "其他", flag: "🌍", value: 0, color: "#6b7280", rawValue: "0%", estimatedVisits: "0", regionName: "Other" },
    ];
  }

  return regions.slice(0, 5).map((region, index) => {
    const mapping = regionMapping[region.region_name] || regionMapping.Other;
    // 如果没有预定义颜色，使用默认颜色数组
    const defaultColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
    const color = mapping.color || defaultColors[index % defaultColors.length];

    return {
      country: mapping.label,
      flag: mapping.flag,
      value: region.traffic_percent,
      color: color,
      rawValue: region.traffic_percent_raw || `${region.traffic_percent.toFixed(1)}%`,
      estimatedVisits: region.estimated_visits,
      regionName: region.region_name,
    };
  });
};

// 根据数据库数据生成关键词分析数据
const generateTopKeywords = (keywords: KeywordAnalysisData[]) => {
  if (!keywords || keywords.length === 0) {
    // 默认数据，当没有数据时显示
    return [
      { keyword: "暂无关键词数据", searches: 0, trend: "0%", trendUp: false, difficulty: "未知", color: "#6b7280", estimatedVisits: "0" },
    ];
  }

  // 难度映射
  const getDifficultyInfo = (difficulty: number | null) => {
    if (!difficulty) return { label: "未知", color: "#6b7280" };
    if (difficulty >= 70) return { label: "高", color: "#ef4444" };
    if (difficulty >= 40) return { label: "中", color: "#f59e0b" };
    return { label: "低", color: "#10b981" };
  };

  return keywords.slice(0, 8).map(keyword => {
    const difficultyInfo = getDifficultyInfo(keyword.keyword_difficulty);
    const searchVolume = parseInt(keyword.search_volume) || 0;
    const estimatedVisits = parseInt(keyword.estimated_visits) || 0;

    // 简单的趋势计算（基于搜索量和访问量的比例）
    const trendRatio = searchVolume > 0 ? (estimatedVisits / searchVolume) * 100 : 0;
    const trend = trendRatio > 0 ? `+${trendRatio.toFixed(1)}%` : "0%";

    return {
      keyword: keyword.keyword,
      searches: searchVolume,
      trend: trend,
      trendUp: trendRatio > 0,
      difficulty: difficultyInfo.label,
      color: difficultyInfo.color,
      estimatedVisits: keyword.estimated_visits,
      trafficPercentRaw: keyword.traffic_percent_raw,
      keywordType: keyword.keyword_type,
    };
  });
};

// 月份映射
const monthMapping: Record<string, string> = {
  '2025-04': '4月',
  '2025-05': '5月',
  '2025-06': '6月',
  '2025-07': '7月',
  '2025-08': '8月',
  '2025-09': '9月',
  '2025-10': '10月',
  '2025-11': '11月',
  '2025-12': '12月',
};

// 根据数据库数据生成月度流量趋势数据
const generateMonthlyTrafficData = (trends: MonthlyTrendData[]) => {
  if (!trends || trends.length === 0) {
    // 默认数据，当没有数据时显示
    return [
      { month: "4月", value: 0, rawValue: "0", growthRate: 0, period: "2025-04" },
      { month: "5月", value: 0, rawValue: "0", growthRate: 0, period: "2025-05" },
      { month: "6月", value: 0, rawValue: "0", growthRate: 0, period: "2025-06" },
    ];
  }

  return trends.map(trend => ({
    month: monthMapping[trend.period] || trend.period,
    value: parseInt(trend.visits) || 0,
    rawValue: trend.visits_raw || "0",
    growthRate: parseFloat(trend.growth_rate) || 0,
    period: trend.period,
  })).sort((a, b) => a.period.localeCompare(b.period)); // 按时间顺序排列
};

interface AnalyticsDashboardProps {
  toolId: string;
}

export function AnalyticsDashboard({ toolId }: AnalyticsDashboardProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [trafficData, setTrafficData] = useState<TrafficOverviewData | null>(null);
  const [trafficSourcesData, setTrafficSourcesData] = useState<TrafficSourceData[]>([]);
  const [monthlyTrendsData, setMonthlyTrendsData] = useState<MonthlyTrendData[]>([]);
  const [regionDistributionData, setRegionDistributionData] = useState<RegionDistributionData[]>([]);
  const [keywordAnalysisData, setKeywordAnalysisData] = useState<KeywordAnalysisData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);

      // 并行获取所有数据
      const [overviewData, sourcesData, trendsData, regionsData, keywordsData] = await Promise.all([
        fetchTrafficData(toolId),
        fetchTrafficSources(toolId),
        fetchMonthlyTrends(toolId),
        fetchRegionDistribution(toolId),
        fetchKeywordAnalysis(toolId)
      ]);

      setTrafficData(overviewData);
      setTrafficSourcesData(sourcesData);
      setMonthlyTrendsData(trendsData);
      setRegionDistributionData(regionsData);
      setKeywordAnalysisData(keywordsData);
      setLoading(false);

      // 延迟动画效果
      const timer = setTimeout(() => setIsLoaded(true), 300);
      return () => clearTimeout(timer);
    };

    loadData();
  }, [toolId]);

  // 生成动态指标数据
  const analyticsMetrics = generateAnalyticsMetrics(trafficData);

  // 生成动态流量来源数据
  const trafficSources = generateTrafficSources(trafficSourcesData);

  // 生成动态月度流量趋势数据
  const monthlyTrafficData = generateMonthlyTrafficData(monthlyTrendsData);

  // 生成动态地理分布数据
  const geographicData = generateGeographicData(regionDistributionData);

  // 生成动态关键词分析数据
  const topKeywords = generateTopKeywords(keywordAnalysisData);

  // 检查是否有任何流量数据
  const hasAnyTrafficData = trafficData ||
    (trafficSourcesData && trafficSourcesData.length > 0) ||
    (monthlyTrendsData && monthlyTrendsData.length > 0) ||
    (regionDistributionData && regionDistributionData.length > 0) ||
    (keywordAnalysisData && keywordAnalysisData.length > 0);

  // 显示加载状态
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {[...Array(6)].map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardContent className="p-4">
                <div className="h-20 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // 如果没有任何流量数据，不显示整个组件
  if (!hasAnyTrafficData) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* 流量监控数据标头 */}
      <h3 className="text-lg font-bold mb-6 text-gray-900 dark:text-white">流量监控数据</h3>

      {/* Key Metrics Row - 只有当有流量概览数据时才显示 */}
      {trafficData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {analyticsMetrics.map((metric) => {
            const Icon = metric.icon;
            const TrendIcon = metric.trendUp ? TrendingUp : TrendingDown;

            return (
              <Card key={metric.label} className="hover:shadow-md transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className={`p-2 rounded-lg bg-${metric.color}-500/10 border border-${metric.color}-500/20`}>
                      <Icon className={`w-4 h-4 text-${metric.color}-600 dark:text-${metric.color}-400`} />
                    </div>
                    <div className={`flex items-center gap-1 text-xs font-medium ${
                      metric.trendUp ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                    }`}>
                      <TrendIcon className="w-3 h-3" />
                      {metric.trend}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold text-foreground">
                      {metric.displayValue}
                    </p>
                    <p className="text-sm text-muted-foreground">{metric.label}</p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Charts Row - 只有当至少有一个图表有数据时才显示 */}
      {((monthlyTrendsData && monthlyTrendsData.length > 0) || (trafficSourcesData && trafficSourcesData.length > 0)) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Traffic Trend - 只有当有月度趋势数据时才显示 */}
          {monthlyTrendsData && monthlyTrendsData.length > 0 && (
          <Card className="hover:shadow-md transition-all duration-300">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                  <LineChart className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-lg">月度流量趋势</CardTitle>
                  <p className="text-sm text-muted-foreground">工具访问量趋势数据</p>
                </div>
              </div>
            </CardHeader>
          <CardContent className="pt-0">
            <div className="h-64 relative">
              <svg className="w-full h-full" viewBox="0 0 400 200">
                {/* Grid lines */}
                {[0, 1, 2, 3, 4].map((i) => (
                  <line
                    key={i}
                    x1="40"
                    y1={40 + i * 30}
                    x2="380"
                    y2={40 + i * 30}
                    stroke="hsl(var(--border))"
                    strokeWidth="1"
                    opacity="0.4"
                    strokeDasharray="2,2"
                  />
                ))}

                {(() => {
                  // 动态计算Y轴范围，突出数据变化
                  const values = monthlyTrafficData.map(item => item.value).filter(v => v > 0);

                  if (values.length === 0) {
                    // 没有数据时的默认显示
                    const yAxisLabels = [10000, 7500, 5000, 2500, 0];
                    return (
                      <>
                        {/* Y-axis labels */}
                        {yAxisLabels.map((value, i) => (
                          <text
                            key={i}
                            x="30"
                            y={45 + i * 30}
                            fill="hsl(var(--muted-foreground))"
                            fontSize="10"
                            className="font-medium"
                            textAnchor="end"
                          >
                            {value === 0 ? "0" : `${(value / 1000).toFixed(0)}K`}
                          </text>
                        ))}
                        <text x="200" y="100" fill="hsl(var(--muted-foreground))" fontSize="12" textAnchor="middle">
                          暂无数据
                        </text>
                      </>
                    );
                  }

                  const minValue = Math.min(...values);
                  const maxValue = Math.max(...values);

                  // 计算数据范围，如果范围太小则扩大以显示变化
                  const dataRange = maxValue - minValue;
                  const padding = Math.max(dataRange * 0.2, maxValue * 0.1); // 至少20%的数据范围或10%的最大值作为padding

                  const yAxisMin = Math.max(0, minValue - padding);
                  const yAxisMax = maxValue + padding;
                  const yAxisRange = yAxisMax - yAxisMin;

                  // 生成Y轴标签
                  const yAxisLabels = [];
                  for (let i = 0; i <= 4; i++) {
                    const value = Math.round(yAxisMin + (yAxisRange * (4 - i)) / 4);
                    yAxisLabels.push(value);
                  }

                  return (
                    <>
                      {/* Y-axis labels */}
                      {yAxisLabels.map((value, i) => (
                        <text
                          key={i}
                          x="30"
                          y={45 + i * 30}
                          fill="hsl(var(--muted-foreground))"
                          fontSize="10"
                          className="font-medium"
                          textAnchor="end"
                        >
                          {value === 0 ? "0" : value >= 1000 ? `${(value / 1000).toFixed(1)}K` : value.toString()}
                        </text>
                      ))}

                      {/* Line chart */}
                      <polyline
                        fill="none"
                        stroke="hsl(var(--primary))"
                        strokeWidth="3"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        points={monthlyTrafficData.map((item, index) => {
                          const x = 60 + (index * 280) / (monthlyTrafficData.length - 1);
                          // 使用相对于最小值的比例来计算Y坐标，突出变化
                          const normalizedValue = yAxisRange > 0 ? (item.value - yAxisMin) / yAxisRange : 0;
                          const y = 160 - normalizedValue * 120;
                          return `${x},${y}`;
                        }).join(' ')}
                      />

                      {/* Data points with hover effects */}
                      {monthlyTrafficData.map((item, index) => {
                        const x = 60 + (index * 280) / (monthlyTrafficData.length - 1);
                        const normalizedValue = yAxisRange > 0 ? (item.value - yAxisMin) / yAxisRange : 0;
                        const y = 160 - normalizedValue * 120;
                        return (
                          <g key={index}>
                            <circle
                              cx={x}
                              cy={y}
                              r="4"
                              fill="hsl(var(--primary))"
                              className="hover:r-6 transition-all cursor-pointer"
                            />
                            {/* 数据点标签 */}
                            <text
                              x={x}
                              y={y - 10}
                              fill="hsl(var(--foreground))"
                              fontSize="9"
                              className="font-medium opacity-0 hover:opacity-100 transition-opacity"
                              textAnchor="middle"
                            >
                              {item.rawValue || (item.value >= 1000 ? `${(item.value / 1000).toFixed(1)}K` : item.value.toString())}
                            </text>
                          </g>
                        );
                      })}

                      {/* X-axis labels */}
                      {monthlyTrafficData.map((item, index) => {
                        const x = 60 + (index * 280) / (monthlyTrafficData.length - 1);
                        return (
                          <text
                            key={index}
                            x={x}
                            y="185"
                            fill="hsl(var(--muted-foreground))"
                            fontSize="10"
                            className="font-medium"
                            textAnchor="middle"
                          >
                            {item.month}
                          </text>
                        );
                      })}

                      {/* 增长率显示 */}
                      {monthlyTrafficData.map((item, index) => {
                        if (index === 0 || !item.growthRate || item.growthRate === 0) return null;

                        const x = 60 + (index * 280) / (monthlyTrafficData.length - 1);
                        const normalizedValue = yAxisRange > 0 ? (item.value - yAxisMin) / yAxisRange : 0;
                        const y = 160 - normalizedValue * 120;
                        const isPositive = item.growthRate > 0;

                        return (
                          <text
                            key={`growth-${index}`}
                            x={x}
                            y={y - 20}
                            fill={isPositive ? "#10b981" : "#ef4444"}
                            fontSize="8"
                            className="font-medium"
                            textAnchor="middle"
                          >
                            {isPositive ? "+" : ""}{item.growthRate.toFixed(1)}%
                          </text>
                        );
                      })}
                    </>
                  );
                })()}
              </svg>
            </div>
          </CardContent>
        </Card>
        )}

        {/* Traffic Sources - 只有当有流量来源数据时才显示 */}
        {trafficSourcesData && trafficSourcesData.length > 0 && (
          <Card className="hover:shadow-md transition-all duration-300">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <PieChart className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-lg">流量来源</CardTitle>
                  <p className="text-sm text-muted-foreground">访问来源分析</p>
                </div>
              </div>
            </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-4">
              {trafficSources.map((source) => (
                <div key={source.source} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: source.color }}
                    />
                    <span className="text-foreground font-medium">{source.source}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                      <div
                        className="h-full rounded-full transition-all duration-1000"
                        style={{
                          backgroundColor: source.color,
                          width: isLoaded ? `${source.value}%` : '0%'
                        }}
                      />
                    </div>
                    <span className="text-foreground font-bold text-sm w-12 text-right">
                      {source.value}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        )}
      </div>
      )}

      {/* Geographic Distribution - 只有当有地理分布数据时才显示 */}
      {regionDistributionData && regionDistributionData.length > 0 && (
        <Card className="hover:shadow-md transition-all duration-300">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                <Globe className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-lg">地理分布</CardTitle>
                <p className="text-sm text-muted-foreground">用户地理位置分析</p>
              </div>
            </div>
          </CardHeader>
        <CardContent className="pt-0">
          <div className="h-64 relative">
            <svg className="w-full h-full" viewBox="0 0 500 240">
              {(() => {
                // 动态计算Y轴范围，突出地理分布数据变化
                const values = geographicData.map(item => item.value).filter(v => v > 0);

                if (values.length === 0) {
                  // 没有数据时的默认显示
                  const yAxisLabels = [30, 25, 20, 15, 10, 5, 0];
                  return (
                    <>
                      {/* Grid lines */}
                      {[0, 1, 2, 3, 4, 5, 6].map((i) => (
                        <line
                          key={i}
                          x1="60"
                          y1={40 + i * 25}
                          x2="480"
                          y2={40 + i * 25}
                          stroke="hsl(var(--border))"
                          strokeWidth="1"
                          opacity="0.4"
                          strokeDasharray="2,2"
                        />
                      ))}

                      {/* Y-axis labels */}
                      {yAxisLabels.map((value, i) => (
                        <text
                          key={i}
                          x="50"
                          y={45 + i * 25}
                          fill="hsl(var(--muted-foreground))"
                          fontSize="10"
                          className="font-medium"
                          textAnchor="end"
                        >
                          {value}%
                        </text>
                      ))}

                      <text x="250" y="120" fill="hsl(var(--muted-foreground))" fontSize="12" textAnchor="middle">
                        暂无地理分布数据
                      </text>
                    </>
                  );
                }

                const maxValue = Math.max(...values);

                // 地理分布数据通常从0开始显示更直观
                let yAxisMin = 0;
                let yAxisMax = maxValue;

                // 如果最大值较小，适当扩大范围以显示细节
                if (maxValue < 10) {
                  yAxisMax = Math.ceil(maxValue / 5) * 5; // 向上取整到5的倍数
                } else if (maxValue < 30) {
                  yAxisMax = Math.ceil(maxValue / 10) * 10; // 向上取整到10的倍数
                } else {
                  yAxisMax = Math.ceil(maxValue / 20) * 20; // 向上取整到20的倍数
                }

                const yAxisRange = yAxisMax - yAxisMin;

                // 生成Y轴标签 (6个刻度)
                const yAxisLabels = [];
                for (let i = 0; i <= 5; i++) {
                  const value = yAxisMin + (yAxisRange * (5 - i)) / 5;
                  yAxisLabels.push(Math.round(value * 10) / 10); // 保留1位小数
                }

                return (
                  <>
                    {/* Grid lines */}
                    {[0, 1, 2, 3, 4, 5].map((i) => (
                      <line
                        key={i}
                        x1="60"
                        y1={40 + i * 30}
                        x2="480"
                        y2={40 + i * 30}
                        stroke="hsl(var(--border))"
                        strokeWidth="1"
                        opacity="0.4"
                        strokeDasharray="2,2"
                      />
                    ))}

                    {/* Y-axis labels */}
                    {yAxisLabels.map((value, i) => (
                      <text
                        key={i}
                        x="50"
                        y={45 + i * 30}
                        fill="hsl(var(--muted-foreground))"
                        fontSize="10"
                        className="font-medium"
                        textAnchor="end"
                      >
                        {value === 0 ? "0" : `${value}%`}
                      </text>
                    ))}

                    {/* Bars */}
                    {geographicData.map((country, index) => {
                      const barWidth = 60;
                      const barSpacing = 80;
                      const x = 80 + index * barSpacing;

                      // 使用动态范围计算柱状图高度
                      const normalizedValue = yAxisRange > 0 ? (country.value - yAxisMin) / yAxisRange : 0;
                      const barHeight = normalizedValue * 150; // 最大高度150px
                      const y = 190 - barHeight;

                      return (
                        <g key={country.country}>
                          {/* Bar */}
                          <rect
                            x={x}
                            y={y}
                            width={barWidth}
                            height={isLoaded ? barHeight : 0}
                            fill={country.color}
                            rx="4"
                            className="transition-all duration-1000 hover:opacity-80 cursor-pointer"
                          />

                          {/* Value label on top of bar */}
                          <text
                            x={x + barWidth / 2}
                            y={y - 5}
                            fill="hsl(var(--foreground))"
                            fontSize="10"
                            className="font-bold"
                            textAnchor="middle"
                          >
                            {country.rawValue || `${country.value.toFixed(1)}%`}
                          </text>

                          {/* Country flag and name */}
                          <text
                            x={x + barWidth / 2}
                            y={220}
                            fill="hsl(var(--muted-foreground))"
                            fontSize="12"
                            className="font-medium"
                            textAnchor="middle"
                          >
                            {country.flag} {country.country}
                          </text>
                        </g>
                      );
                    })}
                  </>
                );
              })()}
            </svg>
          </div>
        </CardContent>
      </Card>
      )}

      {/* Top Keywords Analysis - 只有当有关键词数据时才显示 */}
      {keywordAnalysisData && keywordAnalysisData.length > 0 && (
        <Card className="hover:shadow-md transition-all duration-300">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                <Search className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-lg">热门搜索关键词</CardTitle>
                <p className="text-sm text-muted-foreground">用户搜索行为分析</p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {topKeywords.map((keyword, index) => (
                <div key={keyword.keyword} className="flex items-center justify-between p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs font-bold">
                      {index + 1}
                    </div>
                    <div className="flex flex-col">
                      <span className="text-foreground font-medium">{keyword.keyword}</span>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{keyword.searches.toLocaleString()} 搜索</span>
                        {/* <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${
                          keyword.difficulty === '高' ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400' :
                          keyword.difficulty === '中' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400' :
                          'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                        }`}>
                          {keyword.difficulty}难度
                        </span> */}
                      </div>
                    </div>
                  </div>
                  <div className={`flex items-center gap-1 text-xs font-medium ${
                    keyword.trendUp ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {keyword.trendUp ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                    {keyword.trend}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}