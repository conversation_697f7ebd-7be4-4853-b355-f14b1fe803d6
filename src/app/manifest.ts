import { MetadataRoute } from 'next';

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'Bili-Tool.Com - Your AI Tools Navigator',
    short_name: 'Bili-Tool.Com',
    description: 'Discover the best AI tools and software. Bili-Tool.Com helps you find AI solutions for productivity, creativity, and more.',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#ff6b35',
    orientation: 'portrait-primary',
    scope: '/',
    lang: 'en',
    categories: ['productivity', 'business', 'utilities'],
    icons: [
      {
        src: '/favicon.ico',
        sizes: '16x16 32x32',
        type: 'image/x-icon',
      },
      {
        src: '/icon-192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'maskable',
      },
      {
        src: '/icon-512.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'maskable',
      },
      {
        src: '/apple-touch-icon.png',
        sizes: '180x180',
        type: 'image/png',
      },
    ],
    screenshots: [
      {
        src: '/screenshots/desktop-1.png',
        sizes: '1280x720',
        type: 'image/png',
        form_factor: 'wide',
        label: 'Bili-Tool.Com Desktop View - AI Tools Directory',
      },
      {
        src: '/screenshots/mobile-1.png',
        sizes: '390x844',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'Bili-Tool.Com Mobile View - AI Tools Directory',
      },
    ],
    shortcuts: [
      {
        name: 'Browse AI Tools',
        short_name: 'Tools',
        description: 'Browse all AI tools',
        url: '/tools',
        icons: [
          {
            src: '/icon-96.png',
            sizes: '96x96',
            type: 'image/png',
          },
        ],
      },
      {
        name: 'Categories',
        short_name: 'Categories',
        description: 'Browse AI tool categories',
        url: '/categories',
        icons: [
          {
            src: '/icon-96.png',
            sizes: '96x96',
            type: 'image/png',
          },
        ],
      },
    ],
    related_applications: [],
    prefer_related_applications: false,
  };
}
