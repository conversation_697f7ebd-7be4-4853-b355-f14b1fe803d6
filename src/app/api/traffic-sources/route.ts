import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const toolId = searchParams.get('tool_id');

    if (!toolId) {
      return NextResponse.json(
        { error: 'tool_id parameter is required' },
        { status: 400 }
      );
    }

    // 使用Prisma查询最新的流量来源数据
    const trafficSources = await prisma.trafficSourceAnalysis.findMany({
      where: {
        toolId: toolId,
      },
      orderBy: [
        { statDate: 'desc' },
        { trafficPercent: 'desc' }
      ],
      select: {
        toolId: true,
        sourceType: true,
        trafficPercent: true,
        trafficPercentRaw: true,
        estimatedVisits: true,
        statDate: true,
      },
      // 获取最新日期的所有来源数据
      take: 10,
    });

    if (!trafficSources || trafficSources.length === 0) {
      return NextResponse.json(
        { error: 'No traffic source data found for this tool' },
        { status: 404 }
      );
    }

    // 获取最新日期
    const latestDate = trafficSources[0].statDate;
    
    // 过滤出最新日期的数据
    const latestSources = trafficSources.filter(
      source => source.statDate.getTime() === latestDate.getTime()
    );

    // 转换数据格式以匹配前端期望的格式
    const formattedData = latestSources.map(source => ({
      source_type: source.sourceType,
      traffic_percent: parseFloat(source.trafficPercent.toString()),
      traffic_percent_raw: source.trafficPercentRaw,
      estimated_visits: source.estimatedVisits?.toString() || '0',
      stat_date: source.statDate.toISOString(),
    }));

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Database query error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
