import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const toolId = searchParams.get('tool_id');

    if (!toolId) {
      return NextResponse.json(
        { error: 'tool_id parameter is required' },
        { status: 400 }
      );
    }

    // 使用Prisma查询月度流量趋势数据
    const monthlyTrends = await prisma.trafficMonthlyTrends.findMany({
      where: {
        toolId: toolId,
      },
      orderBy: {
        yearMonth: 'asc',
      },
      select: {
        toolId: true,
        period: true,
        yearMonth: true,
        visits: true,
        visitsRaw: true,
        growthRate: true,
      },
    });

    if (!monthlyTrends || monthlyTrends.length === 0) {
      return NextResponse.json(
        { error: 'No monthly trends data found for this tool' },
        { status: 404 }
      );
    }

    // 转换数据格式以匹配前端期望的格式
    const formattedData = monthlyTrends.map(trend => ({
      period: trend.period,
      year_month: trend.yearMonth.toISOString(),
      visits: trend.visits?.toString() || '0',
      visits_raw: trend.visitsRaw,
      growth_rate: trend.growthRate?.toString() || '0',
    }));

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Database query error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
