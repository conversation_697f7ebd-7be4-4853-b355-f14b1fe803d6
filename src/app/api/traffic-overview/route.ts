import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const toolId = searchParams.get('tool_id');

    if (!toolId) {
      return NextResponse.json(
        { error: 'tool_id parameter is required' },
        { status: 400 }
      );
    }

    // 使用Prisma查询最新的流量数据
    const trafficData = await prisma.trafficSiteOverview.findFirst({
      where: {
        toolId: toolId,
      },
      orderBy: {
        statDate: 'desc',
      },
      select: {
        toolId: true,
        domain: true,
        globalRank: true,
        countryRank: true,
        totalVisits: true,
        totalVisitsRaw: true,
        visitsChangePercent: true,
        avgDurationSeconds: true,
        pagesPerVisit: true,
        bounceRate: true,
        statDate: true,
      },
    });

    if (!trafficData) {
      return NextResponse.json(
        { error: 'No traffic data found for this tool' },
        { status: 404 }
      );
    }

    // 转换数据格式以匹配前端期望的格式
    const formattedData = {
      tool_id: trafficData.toolId,
      domain: trafficData.domain,
      global_rank: trafficData.globalRank,
      country_rank: trafficData.countryRank,
      total_visits: trafficData.totalVisits?.toString() || '0',
      total_visits_raw: trafficData.totalVisitsRaw,
      visits_change_percent: trafficData.visitsChangePercent?.toString() || '0',
      avg_duration_seconds: trafficData.avgDurationSeconds || 0,
      pages_per_visit: trafficData.pagesPerVisit?.toString() || '0',
      bounce_rate: trafficData.bounceRate?.toString() || '0',
      stat_date: trafficData.statDate.toISOString(),
    };

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Database query error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
