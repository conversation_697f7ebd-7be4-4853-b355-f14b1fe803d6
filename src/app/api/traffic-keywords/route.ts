import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const toolId = searchParams.get('tool_id');

    if (!toolId) {
      return NextResponse.json(
        { error: 'tool_id parameter is required' },
        { status: 400 }
      );
    }

    // 使用Prisma查询最新的关键词分析数据
    const keywordAnalysis = await prisma.trafficKeywordAnalysis.findMany({
      where: {
        toolId: toolId,
      },
      orderBy: [
        { statDate: 'desc' },
        { estimatedVisits: 'desc' }
      ],
      select: {
        toolId: true,
        keyword: true,
        keywordType: true,
        trafficPercent: true,
        trafficPercentRaw: true,
        estimatedVisits: true,
        searchVolume: true,
        keywordDifficulty: true,
        averagePosition: true,
        statDate: true,
      },
      // 获取最新日期的所有关键词数据
      take: 20,
    });

    if (!keywordAnalysis || keywordAnalysis.length === 0) {
      return NextResponse.json(
        { error: 'No keyword analysis data found for this tool' },
        { status: 404 }
      );
    }

    // 获取最新日期
    const latestDate = keywordAnalysis[0].statDate;
    
    // 过滤出最新日期的数据
    const latestKeywords = keywordAnalysis.filter(
      keyword => keyword.statDate.getTime() === latestDate.getTime()
    );

    // 转换数据格式以匹配前端期望的格式
    const formattedData = latestKeywords.map(keyword => ({
      keyword: keyword.keyword,
      keyword_type: keyword.keywordType,
      traffic_percent: keyword.trafficPercent?.toString() || '0',
      traffic_percent_raw: keyword.trafficPercentRaw,
      estimated_visits: keyword.estimatedVisits?.toString() || '0',
      search_volume: keyword.searchVolume?.toString() || '0',
      keyword_difficulty: keyword.keywordDifficulty,
      average_position: keyword.averagePosition?.toString() || '0',
      stat_date: keyword.statDate.toISOString(),
    }));

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Database query error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
