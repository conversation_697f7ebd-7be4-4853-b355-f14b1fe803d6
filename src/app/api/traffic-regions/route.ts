import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const toolId = searchParams.get('tool_id');

    if (!toolId) {
      return NextResponse.json(
        { error: 'tool_id parameter is required' },
        { status: 400 }
      );
    }

    // 使用Prisma查询最新的地理分布数据
    const regionDistribution = await prisma.trafficRegionDistribution.findMany({
      where: {
        toolId: toolId,
      },
      orderBy: [
        { statDate: 'desc' },
        { trafficPercent: 'desc' }
      ],
      select: {
        toolId: true,
        regionName: true,
        countryCode: true,
        trafficPercent: true,
        trafficPercentRaw: true,
        estimatedVisits: true,
        rankInRegion: true,
        statDate: true,
      },
      // 获取最新日期的所有地区数据
      take: 20,
    });

    if (!regionDistribution || regionDistribution.length === 0) {
      return NextResponse.json(
        { error: 'No region distribution data found for this tool' },
        { status: 404 }
      );
    }

    // 获取最新日期
    const latestDate = regionDistribution[0].statDate;
    
    // 过滤出最新日期的数据
    const latestRegions = regionDistribution.filter(
      region => region.statDate.getTime() === latestDate.getTime()
    );

    // 转换数据格式以匹配前端期望的格式
    const formattedData = latestRegions.map(region => ({
      region_name: region.regionName,
      country_code: region.countryCode,
      traffic_percent: parseFloat(region.trafficPercent.toString()),
      traffic_percent_raw: region.trafficPercentRaw,
      estimated_visits: region.estimatedVisits?.toString() || '0',
      rank_in_region: region.rankInRegion,
      stat_date: region.statDate.toISOString(),
    }));

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Database query error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
