# BILI-TOOL - AI Tools Directory Platform

<div align="center">

![BILI-TOOL Logo](public/logo.png)

**A comprehensive AI tools directory platform built with Next.js 15**

[![Next.js](https://img.shields.io/badge/Next.js-15.2.3-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Prisma](https://img.shields.io/badge/Prisma-6.1.0-2D3748?style=flat-square&logo=prisma)](https://www.prisma.io/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)

[Live Demo](https://bili-tool.com) • [Documentation](#documentation) • [Contributing](#contributing) • [中文文档](README-zh.md)

</div>

## 🚀 Overview

BILI-TOOL is a modern, full-stack AI tools directory platform that helps users discover, explore, and utilize AI tools across various categories. Built with Next.js 15 and featuring a robust architecture, the platform offers multilingual support, user authentication, payment integration, and a comprehensive tools catalog.

### ✨ Key Features

- 🔍 **Comprehensive AI Tools Directory** - Discover and explore AI tools across multiple categories
- 🌐 **Multilingual Support** - Full internationalization with English and Chinese language support
- 🔐 **Multiple Authentication Methods** - Google OAuth, GitHub OAuth, Google One Tap, and email/password
- 💳 **Integrated Payment System** - Stripe integration for subscriptions and premium features
- 📱 **Responsive Design** - Mobile-first approach with modern UI components
- ⚡ **High Performance** - Optimized with caching, SSR, and image optimization
- 🏷️ **Advanced Categorization** - Hierarchical categories and tagging system
- 🔎 **Powerful Search** - Advanced search and filtering capabilities

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 15.2.3 with App Router
- **Language**: TypeScript 5.0
- **Styling**: Tailwind CSS 3.4.1 + Radix UI components
- **State Management**: React Hooks + Server Components
- **Authentication**: NextAuth.js 4.24.11
- **Internationalization**: next-intl 3.26.3
- **Animations**: Framer Motion 12.6.2

### Backend & Database
- **Database**: PostgreSQL with Prisma ORM 6.1.0
- **API Routes**: Next.js API Routes
- **Payment Processing**: Stripe 17.7.0
- **File Storage**: Cloud-based image storage

### Development & Deployment
- **Package Manager**: pnpm 10.12.1
- **Testing**: Jest 29.7.0 with ts-jest
- **Linting**: ESLint with Next.js config
- **Containerization**: Docker support
- **Build Tool**: Next.js with Turbopack (dev)

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** 18.x or higher
- **pnpm** 8.x or higher (recommended) or npm/yarn
- **PostgreSQL** 13.x or higher
- **Git** for version control

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/wenhaofree/bili-tool.git
cd bili-tool
```

### 2. Install Dependencies

```bash
# Using pnpm (recommended)
pnpm install

# Or using npm
npm install

# Or using yarn
yarn install
```

### 3. Environment Configuration

Create a `.env.local` file in the root directory and configure the following variables:

```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/bili-tool"

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key
AUTH_SECRET=your-auth-secret-key

# OAuth Providers (Optional - enable as needed)
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret

NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret

NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=true

# Stripe Payment Configuration
STRIPE_PRIVATE_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key

# API Configuration (if using external services)
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

### 4. Database Setup

#### Create PostgreSQL Database

```sql
-- Create database
CREATE DATABASE bili-tool;

-- Create user with password
CREATE USER aistak_user WITH ENCRYPTED PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE bili-tool TO aistak_user;

-- Grant schema privileges (for PostgreSQL 15+)
GRANT ALL ON SCHEMA public TO aistak_user;
GRANT CREATE ON SCHEMA public TO aistak_user;
```

#### Initialize Database with Prisma

```bash
# Generate Prisma client
pnpm db:generate

# Push database schema (for development)
pnpm db:push

# Or run migrations (for production)
npx prisma migrate deploy

# Optional: Open Prisma Studio to view data
pnpm db:studio
```

### 5. Start Development Server

```bash
# Start the development server with Turbopack
pnpm dev

# Or using npm
npm run dev

# Or using yarn
yarn dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

### 6. Available Scripts

```bash
# Development
pnpm dev              # Start development server with Turbopack
pnpm build            # Build for production
pnpm start            # Start production server
pnpm lint             # Run ESLint

# Database operations
pnpm db:push          # Push schema changes to database
pnpm db:pull          # Pull schema from database
pnpm db:generate      # Generate Prisma client
pnpm db:studio        # Open Prisma Studio
pnpm db:sync          # Sync database (pull + push + generate)

# Testing
pnpm test:db          # Run database tests
pnpm test:db:setup    # Setup test database
pnpm test:db:docker   # Run tests with Docker

# Docker operations
pnpm docker:up        # Start Docker containers
pnpm docker:down      # Stop Docker containers

# Sitemap
pnpm sitemap          # Generate sitemap
pnpm test:sitemap     # Test sitemap generation
```

## 🏗️ Project Structure

```
bili-tool/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # Internationalized routes
│   │   │   ├── auth/          # Authentication pages
│   │   │   ├── categories/    # Category pages
│   │   │   ├── tools/         # Tools pages
│   │   │   ├── orders/        # Order management
│   │   │   └── profile/       # User profile
│   │   ├── api/               # API routes
│   │   │   ├── auth/          # Authentication endpoints
│   │   │   ├── tools/         # Tools API
│   │   │   ├── categories/    # Categories API
│   │   │   ├── orders/        # Orders API
│   │   │   ├── users/         # Users API
│   │   │   └── stripe/        # Payment endpoints
│   │   └── globals.css        # Global styles
│   ├── components/            # Reusable React components
│   │   ├── ui/               # UI components (Radix UI)
│   │   ├── sections/         # Page sections
│   │   └── blocks/           # Content blocks
│   ├── lib/                  # Utility functions
│   ├── types/                # TypeScript type definitions
│   ├── constants/            # Application constants
│   ├── i18n/                 # Internationalization
│   ├── tests/                # Test files
│   ├── auth.config.ts        # Authentication configuration
│   └── middleware.ts         # Next.js middleware
├── prisma/
│   └── schema.prisma         # Database schema
├── messages/                 # Translation files
│   ├── en.json              # English translations
│   └── zh.json              # Chinese translations
├── public/                   # Static assets
├── docker-compose.yml        # Docker configuration
├── package.json             # Dependencies and scripts
├── tailwind.config.ts       # Tailwind CSS configuration
├── next.config.mjs          # Next.js configuration
└── tsconfig.json            # TypeScript configuration
```

## 🔌 API Endpoints

The application provides a comprehensive REST API built with Next.js API routes:

### Authentication
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User login
- `GET /api/auth/session` - Get current session

### Tools
- `GET /api/tools` - Get tools list with pagination and filtering
- `GET /api/tools/[id]` - Get specific tool details

### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/[slug]` - Get category details and tools

### Users
- `GET /api/users` - Get users (admin only)
- `POST /api/users` - Create user

### Orders & Payments
- `GET /api/orders` - Get user orders
- `POST /api/orders` - Create new order
- `POST /api/stripe` - Create Stripe payment session
- `POST /api/stripe/webhook` - Handle Stripe webhooks

## 🚀 Deployment

### Production Build

```bash
# Build the application
pnpm build

# Start production server
pnpm start
```

### Environment Variables for Production

Ensure all environment variables are properly configured for production:

```env
NODE_ENV=production
NEXTAUTH_URL=https://yourdomain.com
DATABASE_URL=your-production-database-url
# ... other production variables
```

### Docker Deployment

```bash
# Build Docker image
docker build -t bili-tool .

# Run with Docker Compose
docker-compose up -d
```

## 🧪 Testing

The project includes comprehensive testing setup:

```bash
# Run database tests
pnpm test:db

# Setup test database
pnpm test:db:setup

# Run tests with Docker
pnpm test:db:docker

# Debug tests
pnpm test:debug
```

## 🤝 Contributing

We welcome contributions to BILI-TOOL! Please follow these steps:

1. **Fork the repository**
   ```bash
   git clone https://github.com/wenhaofree/bili-tool.git
   ```

2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **Make your changes**
   - Follow the existing code style
   - Add tests for new features
   - Update documentation as needed

4. **Commit your changes**
   ```bash
   git commit -m 'feat: add amazing feature'
   ```

5. **Push to your branch**
   ```bash
   git push origin feature/amazing-feature
   ```

6. **Create a Pull Request**
   - Provide a clear description of your changes
   - Include screenshots for UI changes
   - Ensure all tests pass

### Development Guidelines

- Use TypeScript for type safety
- Follow the existing code structure and naming conventions
- Write meaningful commit messages using conventional commits
- Add appropriate tests for new functionality
- Update documentation for new features

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework for production
- [Prisma](https://www.prisma.io/) - Next-generation ORM for Node.js and TypeScript
- [Tailwind CSS](https://tailwindcss.com/) - A utility-first CSS framework
- [Radix UI](https://www.radix-ui.com/) - Low-level UI primitives
- [NextAuth.js](https://next-auth.js.org/) - Complete open source authentication solution

## � Documentation

### Comprehensive Guides

- [API Documentation](docs/API.md) - Complete API endpoint documentation
- [Development Guide](docs/DEVELOPMENT.md) - Development setup and workflow
- [Deployment Guide](docs/DEPLOYMENT.md) - Multi-platform deployment options
- [Architecture Documentation](docs/ARCHITECTURE.md) - System architecture and design decisions
- [Contributing Guidelines](CONTRIBUTING.md) - How to contribute to the project
- [Changelog](CHANGELOG.md) - Version history and release notes

### Quick References

- [Environment Variables Template](.env.example) - Environment configuration template
- [Database Schema](prisma/schema.prisma) - Prisma database schema
- [TypeScript Configuration](tsconfig.json) - TypeScript project configuration
- [Tailwind Configuration](tailwind.config.ts) - Styling configuration

### Language-Specific Documentation

- [中文文档](README-zh.md) - Chinese documentation
- [中文开发指南](docs/DEVELOPMENT-zh.md) - Chinese development guide

## �📞 Contact & Support

- **Author**: WenHaoFree
- **Email**: <EMAIL>
- **GitHub**: [@wenhaofree](https://github.com/wenhaofree)
- **Project Repository**: [https://github.com/wenhaofree/bili-tool](https://github.com/wenhaofree/bili-tool)

For bug reports and feature requests, please use the [GitHub Issues](https://github.com/wenhaofree/bili-tool/issues) page.

---

<div align="center">

**[⬆ Back to Top](#bili-tool---ai-tools-directory-platform)**

Made with ❤️ by [WenHaoFree](https://github.com/wenhaofree)

</div>
