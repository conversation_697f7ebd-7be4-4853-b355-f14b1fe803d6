# BILI-TOOL - AI工具目录平台

<div align="center">

![BILI-TOOL Logo](public/logo.png)

**基于 Next.js 15 构建的综合性 AI 工具目录平台**

[![Next.js](https://img.shields.io/badge/Next.js-15.2.3-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Prisma](https://img.shields.io/badge/Prisma-6.1.0-2D3748?style=flat-square&logo=prisma)](https://www.prisma.io/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)

[在线演示](https://bili-tool.com) • [文档](#文档) • [贡献指南](#贡献指南)

</div>

## 🚀 项目概述

BILI-TOOL 是一个现代化的全栈 AI 工具目录平台，帮助用户发现、探索和使用各种类别的 AI 工具。基于 Next.js 15 构建，具有强大的架构，提供多语言支持、用户认证、支付集成和全面的工具目录。

### ✨ 核心功能

- 🔍 **全面的 AI 工具目录** - 发现和探索多个类别的 AI 工具
- 🌐 **多语言支持** - 完整的国际化，支持中英文
- 🔐 **多种认证方式** - Google OAuth、GitHub OAuth、Google One Tap 和邮箱密码登录
- 💳 **集成支付系统** - Stripe 集成，支持订阅和高级功能
- 📱 **响应式设计** - 移动优先的现代 UI 组件
- ⚡ **高性能** - 缓存、SSR 和图片优化
- 🏷️ **高级分类系统** - 层次化分类和标签系统
- 🔎 **强大搜索** - 高级搜索和筛选功能

## 🛠️ 技术栈

### 前端技术
- **框架**: Next.js 15.2.3 with App Router
- **语言**: TypeScript 5.0
- **样式**: Tailwind CSS 3.4.1 + Radix UI 组件
- **状态管理**: React Hooks + Server Components
- **认证**: NextAuth.js 4.24.11
- **国际化**: next-intl 3.26.3
- **动画**: Framer Motion 12.6.2

### 后端和数据库
- **数据库**: PostgreSQL with Prisma ORM 6.1.0
- **API 路由**: Next.js API Routes
- **支付处理**: Stripe 17.7.0
- **文件存储**: 云端图片存储

### 开发和部署
- **包管理器**: pnpm 10.12.1
- **测试**: Jest 29.7.0 with ts-jest
- **代码检查**: ESLint with Next.js config
- **容器化**: Docker 支持
- **构建工具**: Next.js with Turbopack (开发环境)

## 📋 环境要求

开始之前，请确保已安装以下软件：

- **Node.js** 18.x 或更高版本
- **pnpm** 8.x 或更高版本（推荐）或 npm/yarn
- **PostgreSQL** 13.x 或更高版本
- **Git** 版本控制工具

## 🚀 快速开始

### 1. 克隆仓库

```bash
git clone https://github.com/wenhaofree/bili-tool.git
cd bili-tool
```

### 2. 安装依赖

```bash
# 使用 pnpm（推荐）
pnpm install

# 或使用 npm
npm install

# 或使用 yarn
yarn install
```

### 3. 环境配置

在根目录创建 `.env.local` 文件并配置以下变量：

```env
# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/bili-tool"

# NextAuth 配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key
AUTH_SECRET=your-auth-secret-key

# OAuth 提供商（可选 - 根据需要启用）
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret

NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret

NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=true

# Stripe 支付配置
STRIPE_PRIVATE_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key

# API 配置（如果使用外部服务）
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

### 4. 数据库设置

#### 创建 PostgreSQL 数据库

```sql
-- 创建数据库
CREATE DATABASE bili-tool;

-- 创建用户并设置密码
CREATE USER aistak_user WITH ENCRYPTED PASSWORD 'your_secure_password';

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE bili-tool TO aistak_user;

-- 授予模式权限（PostgreSQL 15+）
GRANT ALL ON SCHEMA public TO aistak_user;
GRANT CREATE ON SCHEMA public TO aistak_user;
```

#### 使用 Prisma 初始化数据库

```bash
# 生成 Prisma 客户端
pnpm db:generate

# 推送数据库模式（开发环境）
pnpm db:push

# 或运行迁移（生产环境）
npx prisma migrate deploy

# 可选：打开 Prisma Studio 查看数据
pnpm db:studio
```

### 5. 启动开发服务器

```bash
# 使用 Turbopack 启动开发服务器
pnpm dev

# 或使用 npm
npm run dev

# 或使用 yarn
yarn dev
```

应用将在 [http://localhost:3000](http://localhost:3000) 上运行

### 6. 可用脚本

```bash
# 开发
pnpm dev              # 使用 Turbopack 启动开发服务器
pnpm build            # 构建生产版本
pnpm start            # 启动生产服务器
pnpm lint             # 运行 ESLint

# 数据库操作
pnpm db:push          # 推送模式变更到数据库
pnpm db:pull          # 从数据库拉取模式
pnpm db:generate      # 生成 Prisma 客户端
pnpm db:studio        # 打开 Prisma Studio
pnpm db:sync          # 同步数据库（pull + push + generate）

# 测试
pnpm test:db          # 运行数据库测试
pnpm test:db:setup    # 设置测试数据库
pnpm test:db:docker   # 使用 Docker 运行测试

# Docker 操作
pnpm docker:up        # 启动 Docker 容器
pnpm docker:down      # 停止 Docker 容器

# 站点地图
pnpm sitemap          # 生成站点地图
pnpm test:sitemap     # 测试站点地图生成
```

## 🏗️ 项目结构

```
bili-tool/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # 国际化路由
│   │   │   ├── auth/          # 认证页面
│   │   │   ├── categories/    # 分类页面
│   │   │   ├── tools/         # 工具页面
│   │   │   ├── orders/        # 订单管理
│   │   │   └── profile/       # 用户资料
│   │   ├── api/               # API 路由
│   │   │   ├── auth/          # 认证端点
│   │   │   ├── tools/         # 工具 API
│   │   │   ├── categories/    # 分类 API
│   │   │   ├── orders/        # 订单 API
│   │   │   ├── users/         # 用户 API
│   │   │   └── stripe/        # 支付端点
│   │   └── globals.css        # 全局样式
│   ├── components/            # 可复用 React 组件
│   │   ├── ui/               # UI 组件（Radix UI）
│   │   ├── sections/         # 页面区块
│   │   └── blocks/           # 内容块
│   ├── lib/                  # 工具函数
│   ├── types/                # TypeScript 类型定义
│   ├── constants/            # 应用常量
│   ├── i18n/                 # 国际化
│   ├── tests/                # 测试文件
│   ├── auth.config.ts        # 认证配置
│   └── middleware.ts         # Next.js 中间件
├── prisma/
│   └── schema.prisma         # 数据库模式
├── messages/                 # 翻译文件
│   ├── en.json              # 英文翻译
│   └── zh.json              # 中文翻译
├── public/                   # 静态资源
├── docker-compose.yml        # Docker 配置
├── package.json             # 依赖和脚本
├── tailwind.config.ts       # Tailwind CSS 配置
├── next.config.mjs          # Next.js 配置
└── tsconfig.json            # TypeScript 配置
```

## 🔌 API 端点

应用提供基于 Next.js API 路由构建的全面 REST API：

### 认证
- `POST /api/auth/signup` - 用户注册
- `POST /api/auth/signin` - 用户登录
- `GET /api/auth/session` - 获取当前会话

### 工具
- `GET /api/tools` - 获取工具列表（支持分页和筛选）
- `GET /api/tools/[id]` - 获取特定工具详情

### 分类
- `GET /api/categories` - 获取所有分类
- `GET /api/categories/[slug]` - 获取分类详情和工具

### 用户
- `GET /api/users` - 获取用户（仅管理员）
- `POST /api/users` - 创建用户

### 订单和支付
- `GET /api/orders` - 获取用户订单
- `POST /api/orders` - 创建新订单
- `POST /api/stripe` - 创建 Stripe 支付会话
- `POST /api/stripe/webhook` - 处理 Stripe Webhooks

## 🚀 部署

### 生产构建

```bash
# 构建应用
pnpm build

# 启动生产服务器
pnpm start
```

### 生产环境变量

确保为生产环境正确配置所有环境变量：

```env
NODE_ENV=production
NEXTAUTH_URL=https://yourdomain.com
DATABASE_URL=your-production-database-url
# ... 其他生产变量
```

### Docker 部署

```bash
# 构建 Docker 镜像
docker build -t bili-tool .

# 使用 Docker Compose 运行
docker-compose up -d
```

## 🧪 测试

项目包含全面的测试设置：

```bash
# 运行数据库测试
pnpm test:db

# 设置测试数据库
pnpm test:db:setup

# 使用 Docker 运行测试
pnpm test:db:docker

# 调试测试
pnpm test:debug
```

## 🤝 贡献指南

我们欢迎对 BILI-TOOL 的贡献！请遵循以下步骤：

1. **Fork 仓库**
   ```bash
   git clone https://github.com/wenhaofree/bili-tool.git
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **进行更改**
   - 遵循现有代码风格
   - 为新功能添加测试
   - 根据需要更新文档

4. **提交更改**
   ```bash
   git commit -m 'feat: add amazing feature'
   ```

5. **推送到分支**
   ```bash
   git push origin feature/amazing-feature
   ```

6. **创建 Pull Request**
   - 提供清晰的更改描述
   - 为 UI 更改包含截图
   - 确保所有测试通过

### 开发指南

- 使用 TypeScript 确保类型安全
- 遵循现有代码结构和命名约定
- 使用传统提交格式编写有意义的提交消息
- 为新功能添加适当的测试
- 为新功能更新文档

## 📄 许可证

本项目基于 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - 用于生产的 React 框架
- [Prisma](https://www.prisma.io/) - 下一代 Node.js 和 TypeScript ORM
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [Radix UI](https://www.radix-ui.com/) - 低级 UI 原语
- [NextAuth.js](https://next-auth.js.org/) - 完整的开源认证解决方案

## 📚 文档

### 详细文档

- [API 文档](docs/API.md) - 完整的 API 端点文档
- [开发指南](docs/DEVELOPMENT.md) - 开发环境设置和工作流程
- [部署指南](docs/DEPLOYMENT.md) - 多平台部署选项
- [架构文档](docs/ARCHITECTURE.md) - 系统架构和设计决策
- [贡献指南](CONTRIBUTING.md) - 如何为项目做贡献
- [更新日志](CHANGELOG.md) - 版本历史和变更记录

### 快速链接

- [环境配置示例](.env.example) - 环境变量模板
- [数据库模式](prisma/schema.prisma) - Prisma 数据库模式
- [TypeScript 配置](tsconfig.json) - TypeScript 项目配置
- [Tailwind 配置](tailwind.config.ts) - 样式配置

## 📞 联系和支持

- **作者**: WenHaoFree
- **邮箱**: <EMAIL>
- **GitHub**: [@wenhaofree](https://github.com/wenhaofree)
- **项目仓库**: [https://github.com/wenhaofree/bili-tool](https://github.com/wenhaofree/bili-tool)

如需报告错误和功能请求，请使用 [GitHub Issues](https://github.com/wenhaofree/bili-tool/issues) 页面。

---

<div align="center">

**[⬆ 返回顶部](#bili-tool---ai工具目录平台)**

由 [WenHaoFree](https://github.com/wenhaofree) 用 ❤️ 制作

</div>
