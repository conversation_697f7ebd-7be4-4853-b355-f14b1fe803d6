generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  // 自增主键ID
  id             Int       @id @default(autoincrement())
  // 用户唯一标识UUID
  uuid           String    @unique
  // 用户邮箱
  email          String
  // 密码哈希，可选（用于邮箱密码登录）
  password       String?   @db.VarChar(255)
  // 创建时间，自动生成当前时间
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  // 更新时间，自动更新
  updatedAt      DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)
  // 是否已删除标记
  isDeleted      Boolean   @default(false) @map("is_deleted")
  // 用户昵称，可选
  nickname       String?   @db.VarChar(255)
  // 用户头像URL，可选
  avatarUrl      String?   @map("avatar_url") @db.VarChar(255)
  // 用户区域/语言设置，可选
  locale         String?   @db.VarChar(50)
  // 登录类型，可选
  signinType     String?   @map("signin_type") @db.VarChar(50)
  // 登录IP地址，可选
  signinIp       String?   @map("signin_ip") @db.VarChar(255)
  // 登录提供商(如Google、Facebook等)，可选
  signinProvider String?   @map("signin_provider") @db.VarChar(50)
  // 第三方登录的OpenID，可选
  signinOpenid   String?   @map("signin_openid") @db.VarChar(255)
  // 用户订单关联
  orders         Order[]

  // 联合唯一索引：确保同一登录提供商下邮箱唯一
  @@unique([email, signinProvider])
  // 数据库表映射名称
  @@map("users")
}

model Order {
  // 自增主键ID
  id               Int       @id @default(autoincrement())
  // 订单编号，唯一
  orderNo          String    @unique @map("order_no") @db.VarChar(255)
  // 订单创建时间
  createdAt        DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  // 订单更新时间
  updatedAt        DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)
  // 是否已删除标记
  isDeleted        Boolean   @default(false) @map("is_deleted")
  // 用户UUID，关联User表
  userUuid         String    @map("user_uuid") @db.VarChar(255)
  // 用户邮箱
  userEmail        String    @map("user_email") @db.VarChar(255)
  // 订单金额
  amount           Int
  // 计费周期间隔（月、年等）
  interval         String?   @db.VarChar(50)
  // 订单过期时间
  expiredAt        DateTime? @map("expired_at") @db.Timestamptz(6)
  // 订单状态
  status           String    @db.VarChar(50)
  // Stripe支付会话ID
  stripeSessionId  String?   @map("stripe_session_id") @db.VarChar(255)
  // 订单包含的积分数量
  credits          Int
  // 货币类型
  currency         String?   @db.VarChar(50)
  // 订阅ID
  subId            String?   @map("sub_id") @db.VarChar(255)
  // 订阅间隔计数
  subIntervalCount Int?      @map("sub_interval_count")
  // 订阅周期锚点
  subCycleAnchor   Int?      @map("sub_cycle_anchor")
  // 订阅周期结束时间
  subPeriodEnd     Int?      @map("sub_period_end")
  // 订阅周期开始时间
  subPeriodStart   Int?      @map("sub_period_start")
  // 订阅次数
  subTimes         Int?      @map("sub_times")
  // 产品ID
  productId        String?   @map("product_id") @db.VarChar(255)
  // 产品名称
  productName      String?   @map("product_name") @db.VarChar(255)
  // 有效月数
  validMonths      Int?      @map("valid_months")
  // 订单详情（可存储JSON）
  orderDetail      String?   @map("order_detail")
  // 支付时间
  paidAt           DateTime? @map("paid_at") @db.Timestamptz(6)
  // 支付邮箱
  paidEmail        String?   @map("paid_email") @db.VarChar(255)
  // 支付详情
  paidDetail       String?   @map("paid_detail")
  // 关联用户
  user             User      @relation(fields: [userUuid], references: [uuid])

  // 数据库表映射名称
  @@map("orders")
}

/// 工具分类表 - 存储AI工具分类的基本信息
model Category {
  /// 自增主键ID
  id          Int              @id @default(autoincrement())
  /// 分类唯一标识符(英文短横线分隔) 如: ai-code-assistant
  slug        String           @unique @db.VarChar(50)
  /// 分类图标URL
  iconUrl     String?          @map("icon_url") @db.Text
  /// 分类级别 (1: 一级分类, 2: 二级分类)
  level       Int              @default(2) @map("level")
  /// 主分类标识符 (如:text-writing,image-generation等)
  mainCategory String?         @map("main_category") @db.VarChar(50)
  /// 父分类ID (如果是二级分类则关联到一级分类)
  parentId    Int?             @map("parent_id")
  /// 排序权重，值越大越靠前
  weight      Int              @default(0) @map("weight")
  /// 创建时间
  createdAt   DateTime         @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt   DateTime         @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 分类的多语言翻译
  translations CategoryTranslation[]
  /// 关联到该分类的工具
  tools       ToolCategory[]
  /// 父分类关联 (自引用关系)
  parent      Category?        @relation("CategoryHierarchy", fields: [parentId], references: [id], onDelete: SetNull)
  /// 子分类列表
  children    Category[]       @relation("CategoryHierarchy")

  /// 数据库表名映射
  @@map("categories")
  /// 在slug字段上创建索引，加快查询
  @@index([slug])
  /// 在level字段上创建索引，加快按级别查询
  @@index([level])
  /// 在mainCategory字段上创建索引，加快按主分类查询
  @@index([mainCategory])
  /// 在parentId字段上创建索引，加快父子分类查询
  @@index([parentId])
}

/// 分类翻译表 - 存储分类的多语言翻译内容
model CategoryTranslation {
  /// 自增主键ID
  id          Int      @id @default(autoincrement())
  /// 关联的分类ID
  categoryId  Int      @map("category_id")
  /// 语言代码(如'en', 'zh')
  locale      String   @db.VarChar(10)
  /// 分类名称
  name        String   @db.VarChar(100)
  /// 分类描述
  description String?  @db.Text
  /// 创建时间
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 关联到分类表，当分类被删除时级联删除翻译
  category    Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  /// 确保同一分类在同一语言下只有一个翻译
  @@unique([categoryId, locale])
  /// 数据库表名映射
  @@map("category_translations")
  /// 在语言代码上创建索引，加快按语言查询
  @@index([locale])
  /// 组合索引，加快查询特定分类的特定语言翻译
  @@index([categoryId, locale])
}

/// 标签表 - 存储AI工具标签的基本信息
model Tag {
  /// 自增主键ID
  id          Int       @id @default(autoincrement())
  /// 标签唯一标识符(英文短横线分隔) 如: ai-tool
  slug        String    @unique @db.VarChar(50)
  /// 创建时间
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 标签的多语言翻译
  translations TagTranslation[]
  /// 使用该标签的工具
  tools       ToolTag[]

  /// 数据库表名映射
  @@map("tags")
  /// 在slug字段上创建索引，加快标签查询
  @@index([slug])
}

/// 标签翻译表 - 存储标签的多语言翻译内容
model TagTranslation {
  /// 自增主键ID
  id        Int      @id @default(autoincrement())
  /// 关联的标签ID
  tagId     Int      @map("tag_id")
  /// 语言代码(如'en', 'zh')
  locale    String   @db.VarChar(10)
  /// 标签名称
  name      String   @db.VarChar(50)
  /// 创建时间
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 关联到标签表，当标签被删除时级联删除翻译
  tag       Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  /// 确保同一标签在同一语言下只有一个翻译
  @@unique([tagId, locale])
  /// 数据库表名映射
  @@map("tag_translations")
  /// 在语言代码上创建索引，加快按语言查询
  @@index([locale])
  /// 组合索引，加快查询特定标签的特定语言翻译
  @@index([tagId, locale])
}

/// 工具表 - 存储AI工具的基本信息
model Tool {
  /// 自增主键ID
  id          Int          @id @default(autoincrement())
  /// 工具唯一标识符(英文短横线分隔) 如: chatgpt
  toolId      String       @unique @map("tool_id") @db.VarChar(50)
  /// 工具图标URL
  iconUrl     String       @map("icon_url") @db.Text
  /// 工具官方网站URL
  url         String       @db.Text
  /// logo的图片
  websiteLogo String?      @map("website_logo") @db.VarChar(255)
  /// 域名注册时间
  domainRegistrationDate DateTime? @map("domain_registration_date") @db.Timestamp(0)
  /// 价格类型(free/freemium/paid/subscription)
  pricingType String       @map("pricing_type") @db.VarChar(20)
  /// 是否为付费工具
  isPremium   Boolean      @default(false) @map("is_premium")
  /// 是否为新上线工具
  isNew       Boolean      @default(false) @map("is_new")
  /// 是否为推荐工具
  isFeatured  Boolean      @default(false) @map("is_featured")
  /// 工具评分(1.0-5.0)
  rating      Decimal?     @db.Decimal(2,1)
  /// 是否提供API接口
  apiAvailable Boolean      @default(false) @map("api_available")
  /// 开发者/发布者名称
  publisher   String?      @db.VarChar(100)
  /// 发布者网站URL
  publisherUrl String?      @map("publisher_url") @db.Text
  /// 服务条款网址
  termsUrl    String?      @map("terms_url") @db.Text
  /// 隐私政策网址
  privacyUrl  String?      @map("privacy_url") @db.Text
  /// 创建时间
  createdAt   DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt   DateTime     @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 工具的多语言翻译
  translations ToolTranslation[]
  /// 工具所属的分类
  categories  ToolCategory[]
  /// 工具相关标签
  tags        ToolTag[]
  /// 工具的功能特性
  features    ToolFeature[]

  /// 数据库表名映射
  @@map("tools")
  /// 在toolId字段上创建索引加快查询
  @@index([toolId])
  /// 在isPremium字段上创建索引，加快查询付费工具
  @@index([isPremium])
  /// 在isNew字段上创建索引，加快查询新工具
  @@index([isNew])
  /// 在isFeatured字段上创建索引，加快查询推荐工具
  @@index([isFeatured])
}

/// 工具翻译表 - 存储工具的多语言翻译内容
model ToolTranslation {
  /// 自增主键ID
  id                Int      @id @default(autoincrement())
  /// 关联的工具ID
  toolId            String   @map("tool_id") @db.VarChar(50)
  /// 语言代码(如'en', 'zh')
  locale            String   @db.VarChar(10)
  /// 工具名称
  name              String   @db.VarChar(100)
  /// 工具简短描述
  description       String   @db.Text
  /// 工具详细描述
  longDescription   String?  @map("long_description") @db.Text
  /// 使用说明和指南
  usageInstructions String?  @map("usage_instructions") @db.Text
  /// 价格详情
  pricingDetails    String?  @map("pricing_details") @db.Text
  /// 集成信息和API说明
  integrationInfo   String?  @map("integration_info") @db.Text
  /// 创建时间
  createdAt         DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt         DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 关联到工具表，当工具被删除时级联删除翻译
  tool              Tool     @relation(fields: [toolId], references: [toolId], onDelete: Cascade)

  /// 确保同一工具在同一语言下只有一个翻译
  @@unique([toolId, locale])
  /// 数据库表名映射
  @@map("tool_translations")
  /// 在语言代码上创建索引，加快按语言查询
  @@index([locale])
  /// 组合索引，加快查询特定工具的特定语言翻译
  @@index([toolId, locale])
}

/// 工具功能特性表 - 存储工具的具体功能特点
model ToolFeature {
  /// 自增主键ID
  id        Int      @id @default(autoincrement())
  /// 关联的工具ID
  toolId    String   @map("tool_id") @db.VarChar(50)
  /// 语言代码(如'en', 'zh')
  locale    String   @db.VarChar(10)
  /// 功能特性描述
  feature   String   @db.VarChar(100)
  /// 创建时间
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 关联到工具表，当工具被删除时级联删除功能
  tool      Tool     @relation(fields: [toolId], references: [toolId], onDelete: Cascade)

  /// 确保同一工具在同一语言下的同一功能只记录一次
  @@unique([toolId, locale, feature])
  /// 数据库表名映射
  @@map("tool_features")
  /// 组合索引，加快查询特定工具的特定语言功能
  @@index([toolId, locale])
}

/// 工具-分类关联表 - 存储工具与分类的多对多关系
model ToolCategory {
  /// 工具ID(复合主键之一)
  toolId     String   @map("tool_id") @db.VarChar(50)
  /// 分类ID(复合主键之一)
  categoryId Int      @map("category_id")
  /// 创建时间
  createdAt  DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 关联到工具表，当工具被删除时级联删除关联
  tool       Tool     @relation(fields: [toolId], references: [toolId], onDelete: Cascade)
  /// 关联到分类表，当分类被删除时级联删除关联
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  /// 设置复合主键，确保工具和分类的关联唯一
  @@id([toolId, categoryId])
  /// 数据库表名映射
  @@map("tool_categories")
  /// 在toolId字段上创建索引，加快查询特定工具的所有分类
  @@index([toolId])
  /// 在categoryId字段上创建索引，加快查询特定分类的所有工具
  @@index([categoryId])
}

/// 工具-标签关联表 - 存储工具与标签的多对多关系
model ToolTag {
  /// 工具ID(复合主键之一)
  toolId    String   @map("tool_id") @db.VarChar(50)
  /// 标签ID(复合主键之一)
  tagId     Int      @map("tag_id")
  /// 创建时间
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 关联到工具表，当工具被删除时级联删除关联
  tool      Tool     @relation(fields: [toolId], references: [toolId], onDelete: Cascade)
  /// 关联到标签表，当标签被删除时级联删除关联
  tag       Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  /// 设置复合主键，确保工具和标签的关联唯一
  @@id([toolId, tagId])
  /// 数据库表名映射
  @@map("tool_tags")
  /// 在toolId字段上创建索引，加快查询特定工具的所有标签
  @@index([toolId])
  /// 在tagId字段上创建索引，加快查询特定标签的所有工具
  @@index([tagId])
}

/// 流量站点概览表 - 存储网站流量统计数据
model TrafficSiteOverview {
  /// 自增主键ID
  id                    Int       @id @default(autoincrement())
  /// 工具ID，关联到工具表
  toolId                String    @map("tool_id") @db.VarChar(50)
  /// 域名
  domain                String    @db.VarChar(255)
  /// 全球排名
  globalRank            Int?      @map("global_rank")
  /// 国家排名
  countryRank           Int?      @map("country_rank")
  /// 国家代码
  countryCode           String?   @default("US") @map("country_code") @db.VarChar(3)
  /// 总访问量
  totalVisits           BigInt?   @default(0) @map("total_visits")
  /// 总访问量原始格式
  totalVisitsRaw        String?   @map("total_visits_raw") @db.VarChar(20)
  /// 访问量变化百分比
  visitsChangePercent   Decimal?  @map("visits_change_percent") @db.Decimal(10,2)
  /// 平均访问时长(秒)
  avgDurationSeconds    Int?      @map("avg_duration_seconds")
  /// 平均访问时长原始格式
  avgDurationRaw        String?   @map("avg_duration_raw") @db.VarChar(20)
  /// 每次访问页面数
  pagesPerVisit         Decimal?  @map("pages_per_visit") @db.Decimal(8,2)
  /// 跳出率
  bounceRate            Decimal?  @map("bounce_rate") @db.Decimal(8,2)
  /// 域名创建日期
  domainCreationDate    DateTime? @map("domain_creation_date") @db.Date
  /// 域名过期日期
  domainExpirationDate  DateTime? @map("domain_expiration_date") @db.Date
  /// 域名最后修改日期
  domainLastChanged     DateTime? @map("domain_last_changed") @db.Date
  /// 域名年龄(年)
  domainAgeYears        Decimal?  @map("domain_age_years") @db.Decimal(8,2)
  /// 数据源
  dataSource            String?   @default("traffic.cv") @map("data_source") @db.VarChar(50)
  /// 源URL
  sourceUrl             String?   @map("source_url") @db.Text
  /// 提取方法
  extractionMethod      String?   @map("extraction_method") @db.VarChar(100)
  /// 统计日期
  statDate              DateTime  @map("stat_date") @db.Date
  /// 创建时间
  createdAt             DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt             DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  /// 数据库表名映射
  @@map("traffic_site_overview")
  /// 在toolId字段上创建索引，加快查询特定工具的流量数据
  @@index([toolId])
  /// 在statDate字段上创建索引，加快按日期查询
  @@index([statDate])
  /// 组合索引，加快查询特定工具的特定日期数据
  @@index([toolId, statDate])
}

/// 流量来源分析表 - 存储网站流量来源统计数据
model TrafficSourceAnalysis {
  /// 自增主键ID
  id                  Int       @id @default(autoincrement())
  /// 工具ID，关联到工具表
  toolId              String    @map("tool_id") @db.VarChar(50)
  /// 流量来源类型 (search, direct, social, referrals, paidReferrals, mail等)
  sourceType          String    @map("source_type") @db.VarChar(50)
  /// 流量百分比
  trafficPercent      Decimal   @map("traffic_percent") @db.Decimal(8,2)
  /// 流量百分比原始格式
  trafficPercentRaw   String?   @map("traffic_percent_raw") @db.VarChar(10)
  /// 预估访问量
  estimatedVisits     BigInt?   @map("estimated_visits")
  /// 统计日期
  statDate            DateTime  @map("stat_date") @db.Date
  /// 数据源
  dataSource          String?   @default("traffic.cv") @map("data_source") @db.VarChar(50)
  /// 创建时间
  createdAt           DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt           DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  /// 数据库表名映射
  @@map("traffic_source_analysis")
  /// 在toolId字段上创建索引，加快查询特定工具的流量来源数据
  @@index([toolId])
  /// 在statDate字段上创建索引，加快按日期查询
  @@index([statDate])
  /// 组合索引，加快查询特定工具的特定日期数据
  @@index([toolId, statDate])
  /// 组合索引，加快查询特定工具的特定来源类型数据
  @@index([toolId, sourceType])
}

/// 流量月度趋势表 - 存储网站月度流量趋势数据
model TrafficMonthlyTrends {
  /// 自增主键ID
  id          Int       @id @default(autoincrement())
  /// 工具ID，关联到工具表
  toolId      String    @map("tool_id") @db.VarChar(50)
  /// 时间周期 (格式: YYYY-MM)
  period      String    @db.VarChar(7)
  /// 年月日期
  yearMonth   DateTime  @map("year_month") @db.Date
  /// 访问量
  visits      BigInt?   @default(0)
  /// 访问量原始格式
  visitsRaw   String?   @map("visits_raw") @db.VarChar(20)
  /// 增长率
  growthRate  Decimal?  @map("growth_rate") @db.Decimal(12,4)
  /// 数据源
  dataSource  String?   @default("traffic.cv") @map("data_source") @db.VarChar(50)
  /// 创建时间
  createdAt   DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt   DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  /// 数据库表名映射
  @@map("traffic_monthly_trends")
  /// 在toolId字段上创建索引，加快查询特定工具的月度趋势数据
  @@index([toolId])
  /// 在yearMonth字段上创建索引，加快按月份查询
  @@index([yearMonth])
  /// 组合索引，加快查询特定工具的特定月份数据
  @@index([toolId, yearMonth])
}

/// 流量地理分布表 - 存储网站流量地理分布数据
model TrafficRegionDistribution {
  /// 自增主键ID
  id                Int       @id @default(autoincrement())
  /// 工具ID，关联到工具表
  toolId            String    @map("tool_id") @db.VarChar(50)
  /// 地区名称
  regionName        String    @map("region_name") @db.VarChar(100)
  /// 国家代码 (ISO 3166-1 alpha-3)
  countryCode       String?   @map("country_code") @db.VarChar(3)
  /// 流量百分比
  trafficPercent    Decimal   @map("traffic_percent") @db.Decimal(8,2)
  /// 流量百分比原始格式
  trafficPercentRaw String?   @map("traffic_percent_raw") @db.VarChar(10)
  /// 预估访问量
  estimatedVisits   BigInt?   @map("estimated_visits")
  /// 地区内排名
  rankInRegion      Int?      @map("rank_in_region")
  /// 统计日期
  statDate          DateTime  @map("stat_date") @db.Date
  /// 数据源
  dataSource        String?   @default("traffic.cv") @map("data_source") @db.VarChar(50)
  /// 创建时间
  createdAt         DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt         DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  /// 数据库表名映射
  @@map("traffic_region_distribution")
  /// 在toolId字段上创建索引，加快查询特定工具的地理分布数据
  @@index([toolId])
  /// 在statDate字段上创建索引，加快按日期查询
  @@index([statDate])
  /// 组合索引，加快查询特定工具的特定日期数据
  @@index([toolId, statDate])
  /// 在countryCode字段上创建索引，加快按国家查询
  @@index([countryCode])
}

/// 流量关键词分析表 - 存储网站流量关键词分析数据
model TrafficKeywordAnalysis {
  /// 自增主键ID
  id                Int       @id @default(autoincrement())
  /// 工具ID，关联到工具表
  toolId            String    @map("tool_id") @db.VarChar(50)
  /// 关键词
  keyword           String    @db.VarChar(255)
  /// 关键词类型 (organic, paid等)
  keywordType       String?   @default("organic") @map("keyword_type") @db.VarChar(20)
  /// 流量百分比
  trafficPercent    Decimal?  @map("traffic_percent") @db.Decimal(8,2)
  /// 流量百分比原始格式
  trafficPercentRaw String?   @map("traffic_percent_raw") @db.VarChar(10)
  /// 预估访问量
  estimatedVisits   BigInt?   @map("estimated_visits")
  /// 搜索量
  searchVolume      BigInt?   @map("search_volume")
  /// 关键词难度
  keywordDifficulty Int?      @map("keyword_difficulty")
  /// 平均排名
  averagePosition   Decimal?  @map("average_position") @db.Decimal(8,2)
  /// 统计日期
  statDate          DateTime  @map("stat_date") @db.Date
  /// 数据源
  dataSource        String?   @default("traffic.cv") @map("data_source") @db.VarChar(50)
  /// 创建时间
  createdAt         DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt         DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  /// 数据库表名映射
  @@map("traffic_keyword_analysis")
  /// 在toolId字段上创建索引，加快查询特定工具的关键词分析数据
  @@index([toolId])
  /// 在statDate字段上创建索引，加快按日期查询
  @@index([statDate])
  /// 组合索引，加快查询特定工具的特定日期数据
  @@index([toolId, statDate])
  /// 在keyword字段上创建索引，加快关键词搜索
  @@index([keyword])
  /// 在keywordType字段上创建索引，加快按类型查询
  @@index([keywordType])
}
